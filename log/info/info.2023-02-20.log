2023-02-20 14:34:55.783 ziyun [main] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on DESKTOP-EAM764B with PID 2092 (started by karl in D:\project\Java Projectes\queuingsystem)
2023-02-20 14:34:55.786 ziyun [main] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-02-20 14:34:58.520 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-02-20 14:34:58.523 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-02-20 14:34:58.647 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 101ms. Found 0 repository interfaces.
2023-02-20 14:34:59.156 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$df4a35f5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:34:59.345 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:34:59.348 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$48d8a6c6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:34:59.354 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:34:59.359 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:34:59.360 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$33847ee0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:34:59.836 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$735d9834] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:34:59.854 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:34:59.857 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:34:59.913 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:34:59.920 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$6e98c1c6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:34:59.924 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:34:59.930 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$1d12a28f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:34:59.937 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:34:59.938 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:34:59.944 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-02-20 14:34:59.977 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-02-20 14:35:00.350 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-02-20 14:35:00.350 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-02-20 14:35:00.350 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-02-20 14:35:00.350 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-02-20 14:35:00.350 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:00.361 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:00.368 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.140 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.147 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.150 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.153 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy114] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.181 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.182 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy116] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.190 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.191 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.193 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.241 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.256 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.276 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.277 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy121] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.280 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.302 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.304 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.334 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.336 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.339 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.355 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.371 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$3addb586] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.375 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$ed2a779f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.377 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$a8415d8e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.388 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.392 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.397 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$cf0cd4bc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.401 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.403 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$1ce95315] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.411 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.412 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.416 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.419 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.438 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.457 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.462 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.462 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.469 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.481 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:35:01.834 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2023-02-20 14:35:01.847 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2023-02-20 14:35:01.855 ziyun [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-02-20 14:35:01.856 ziyun [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2023-02-20 14:35:02.005 ziyun [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-02-20 14:35:02.005 ziyun [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6172 ms
2023-02-20 14:35:02.620 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-02-20 14:35:02.637 ziyun [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-02-20 14:35:02.637 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-02-20 14:35:02.640 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-02-20 14:35:02.642 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-02-20 14:35:02.642 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'DESKTOP-EAM764B1676874902621'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-02-20 14:35:02.642 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-02-20 14:35:02.642 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-02-20 14:35:02.643 ziyun [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@19a1205
2023-02-20 14:35:02.692 ziyun [main] INFO  p6spy - 2023-02-20 14:35:02 | 耗时 14 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-02-20 14:35:03.542 ziyun [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-02-20 14:35:03.647 ziyun [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-02-20 14:35:03.677 ziyun [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-02-20 14:35:04.427 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-02-20 14:35:04.443 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-02-20 14:35:04.483 ziyun [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-02-20 14:35:04.608 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-02-20 14:35:04.617 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-02-20 14:35:04.626 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-02-20 14:35:04.630 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-02-20 14:35:04.642 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-02-20 14:35:04.675 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-02-20 14:35:04.679 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-02-20 14:35:04.687 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-02-20 14:35:04.701 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-02-20 14:35:04.703 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-02-20 14:35:04.709 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-02-20 14:35:04.723 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_10
2023-02-20 14:35:04.752 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2023-02-20 14:35:04.769 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2023-02-20 14:35:04.771 ziyun [main] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 9.713 seconds (JVM running for 15.812)
2023-02-20 14:35:04.779 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-02-20 14:35:04.790 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-02-20 14:35:04.790 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-02-20 14:35:04.824 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 3 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-02-20 14:35:04.839 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 11 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-02-20 14:35:04.879 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-02-20 14:35:04.889 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 3 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-02-20 14:35:04.896 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 2 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166' ;
2023-02-20 14:35:04.898 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-02-20 14:35:04.901 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-02-20 14:35:04.903 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-02-20 14:35:04.904 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167' ;
2023-02-20 14:35:04.905 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-02-20 14:35:04.910 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-02-20 14:35:04.911 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-02-20 14:35:04.913 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171' ;
2023-02-20 14:35:04.915 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-02-20 14:35:04.917 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-02-20 14:35:04.918 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-02-20 14:35:04.921 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172' ;
2023-02-20 14:35:04.922 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-02-20 14:35:04.925 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-02-20 14:35:04.927 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-02-20 14:35:04.928 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174' ;
2023-02-20 14:35:04.929 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-02-20 14:35:04.933 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-02-20 14:35:04.934 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-02-20 14:35:04.935 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175' ;
2023-02-20 14:35:04.937 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-02-20 14:35:04.939 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-02-20 14:35:04.942 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-02-20 14:35:04.943 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176' ;
2023-02-20 14:35:04.951 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-02-20 14:35:04.954 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-02-20 14:35:04.955 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-02-20 14:35:04.956 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177' ;
2023-02-20 14:35:04.959 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-02-20 14:35:04.961 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-02-20 14:35:04.962 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-02-20 14:35:04.963 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178' ;
2023-02-20 14:35:04.965 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-02-20 14:35:04.967 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-02-20 14:35:04.968 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-02-20 14:35:04.970 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179' ;
2023-02-20 14:35:04.975 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 4 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-02-20 14:35:04.977 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-02-20 14:35:04.984 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-02-20 14:35:04.991 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180' ;
2023-02-20 14:35:04.994 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-02-20 14:35:04.997 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-02-20 14:35:05.000 ziyun [main] INFO  p6spy - 2023-02-20 14:35:04 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-02-20 14:35:05.005 ziyun [main] INFO  p6spy - 2023-02-20 14:35:05 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181' ;
2023-02-20 14:35:05.007 ziyun [main] INFO  p6spy - 2023-02-20 14:35:05 | 耗时 0 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'phonedispose' group by u.username;
2023-02-20 14:35:05.009 ziyun [main] INFO  p6spy - 2023-02-20 14:35:05 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'phonedispose';
2023-02-20 14:35:05.010 ziyun [main] INFO  p6spy - 2023-02-20 14:35:05 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'phonedispose' and m.perms is not null and m.perms <> '';
2023-02-20 14:35:05.012 ziyun [main] INFO  p6spy - 2023-02-20 14:35:05 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='182' ;
2023-02-20 14:35:05.012 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-02-20 14:35:05.012 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-02-20 14:35:05.012 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-02-20 14:35:05.012 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-02-20 14:35:05.012 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-02-20T14:35:05.012
2023-02-20 14:38:06.484 ziyun [http-nio-9528-exec-6] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-02-20 14:38:06.484 ziyun [http-nio-9528-exec-6] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2023-02-20 14:38:06.493 ziyun [http-nio-9528-exec-6] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 9 ms
2023-02-20 14:38:06.555 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 14:38:06 | 耗时 12 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-20 14:38:06.613 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-20 14:38:06 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 14:38:06.614 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 14:38:06 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 14:38:06.644 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 14:38:06 | 耗时 7 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 14:38:06.650 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 14:38:06 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 14:38:14.273 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 14:38:14 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 14:38:14.274 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 14:38:14 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 14:38:14.275 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 14:38:14 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 14:38:14.275 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 14:38:14 | 耗时 2 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-20 14:38:14.278 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 14:38:14 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 14:42:30.281 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 14:42:30 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 14:42:30.285 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-20 14:42:30 | 耗时 4 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 14:42:30.287 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-20 14:42:30 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 14:43:17.917 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 14:43:17 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 14:43:17.918 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 14:43:17 | 耗时 3 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 14:43:17.919 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 14:43:17 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE TITLE = '3074';
2023-02-20 14:43:17.922 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 14:43:17 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 14:44:01.859 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 14:44:01 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 14:44:01.862 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 14:44:01 | 耗时 3 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 14:44:01.863 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 14:44:01 | 耗时 4 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE TITLE = '3074';
2023-02-20 14:44:01.865 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 14:44:01 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 14:45:23.186 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-20 14:45:23 | 耗时 4 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info;
2023-02-20 14:45:23.188 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-20 14:45:23 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info ORDER BY REPAIR_ID DESC LIMIT 0,10;
2023-02-20 14:46:14.016 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 14:46:14 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE TITLE = '"3076"';
2023-02-20 14:46:19.724 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 14:46:19 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE TITLE = '3076';
2023-02-20 14:46:42.105 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 14:46:42 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE TITLE = '"3076"';
2023-02-20 14:46:48.235 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 14:46:48 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE TITLE = '3087';
2023-02-20 14:47:41.870 ziyun [Thread-3] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2023-02-20 14:47:41.873 ziyun [Thread-3] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2023-02-20 14:47:41.874 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676874902621 shutting down.
2023-02-20 14:47:41.874 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676874902621 paused.
2023-02-20 14:47:41.874 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676874902621 shutdown complete.
2023-02-20 14:47:41.877 ziyun [Thread-3] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2023-02-20 14:47:41.877 ziyun [Thread-3] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2023-02-20 14:47:41.884 ziyun [Thread-3] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
2023-02-20 14:47:54.172 ziyun [main] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on DESKTOP-EAM764B with PID 17960 (started by karl in D:\project\Java Projectes\queuingsystem)
2023-02-20 14:47:54.175 ziyun [main] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-02-20 14:47:55.745 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-02-20 14:47:55.748 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-02-20 14:47:55.858 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 97ms. Found 0 repository interfaces.
2023-02-20 14:47:56.390 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$c4143b41] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:56.613 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:56.619 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$2da2ac12] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:56.628 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:56.633 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:56.634 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$184e842c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:57.175 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$58279d80] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:57.194 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:57.197 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:57.276 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:57.287 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$5362c712] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:57.292 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:57.300 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$1dca7db] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:57.311 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:57.313 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:57.316 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-02-20 14:47:57.367 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-02-20 14:47:57.824 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-02-20 14:47:57.824 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-02-20 14:47:57.824 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-02-20 14:47:57.824 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-02-20 14:47:57.824 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:57.840 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:57.850 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:58.857 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:58.866 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:58.872 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:58.875 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy114] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:58.916 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:58.919 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy116] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:58.931 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:58.933 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:58.936 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:58.979 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.007 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.036 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.038 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy121] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.042 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.069 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.071 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.129 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.131 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.138 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.171 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.198 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$1fa7bad2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.206 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$d1f47ceb] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.209 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$8d0b62da] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.220 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.225 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.231 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$b3d6da08] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.239 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.242 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$1b35861] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.253 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.255 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.259 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.266 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.296 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.315 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.323 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.324 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.333 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.359 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:47:59.805 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2023-02-20 14:47:59.818 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2023-02-20 14:47:59.826 ziyun [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-02-20 14:47:59.827 ziyun [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2023-02-20 14:48:00.029 ziyun [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-02-20 14:48:00.029 ziyun [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5807 ms
2023-02-20 14:48:00.814 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-02-20 14:48:00.826 ziyun [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-02-20 14:48:00.826 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-02-20 14:48:00.829 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-02-20 14:48:00.831 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-02-20 14:48:00.831 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'DESKTOP-EAM764B1676875680815'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-02-20 14:48:00.832 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-02-20 14:48:00.832 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-02-20 14:48:00.833 ziyun [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@292d71
2023-02-20 14:48:00.886 ziyun [main] INFO  p6spy - 2023-02-20 14:48:00 | 耗时 11 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-02-20 14:48:02.076 ziyun [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-02-20 14:48:02.195 ziyun [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-02-20 14:48:02.238 ziyun [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-02-20 14:48:03.189 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-02-20 14:48:03.206 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-02-20 14:48:03.248 ziyun [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-02-20 14:48:03.393 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-02-20 14:48:03.408 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-02-20 14:48:03.420 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-02-20 14:48:03.426 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-02-20 14:48:03.443 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-02-20 14:48:03.490 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-02-20 14:48:03.494 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-02-20 14:48:03.513 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-02-20 14:48:03.523 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-02-20 14:48:03.525 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-02-20 14:48:03.533 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-02-20 14:48:03.561 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_10
2023-02-20 14:48:03.593 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2023-02-20 14:48:03.617 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2023-02-20 14:48:03.623 ziyun [main] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 10.101 seconds (JVM running for 16.161)
2023-02-20 14:48:03.644 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-02-20 14:48:03.662 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-02-20 14:48:03.662 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-02-20 14:48:03.716 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 2 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-02-20 14:48:03.731 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 6 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-02-20 14:48:03.789 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-02-20 14:48:03.800 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-02-20 14:48:03.819 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 11 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166' ;
2023-02-20 14:48:03.824 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 2 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-02-20 14:48:03.829 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-02-20 14:48:03.833 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-02-20 14:48:03.836 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167' ;
2023-02-20 14:48:03.840 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 2 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-02-20 14:48:03.847 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-02-20 14:48:03.850 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-02-20 14:48:03.853 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171' ;
2023-02-20 14:48:03.858 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 2 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-02-20 14:48:03.860 ziyun [http-nio-9528-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-02-20 14:48:03.861 ziyun [http-nio-9528-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2023-02-20 14:48:03.864 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-02-20 14:48:03.868 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 2 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-02-20 14:48:03.872 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172' ;
2023-02-20 14:48:03.876 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-02-20 14:48:03.879 ziyun [http-nio-9528-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 18 ms
2023-02-20 14:48:03.881 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-02-20 14:48:03.884 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-02-20 14:48:03.887 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174' ;
2023-02-20 14:48:03.893 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 2 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-02-20 14:48:03.898 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-02-20 14:48:03.903 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 2 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-02-20 14:48:03.907 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175' ;
2023-02-20 14:48:03.910 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-02-20 14:48:03.914 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-02-20 14:48:03.920 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 2 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-02-20 14:48:03.924 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176' ;
2023-02-20 14:48:03.928 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 2 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-02-20 14:48:03.936 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 2 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-02-20 14:48:03.939 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-02-20 14:48:03.942 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177' ;
2023-02-20 14:48:03.945 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-02-20 14:48:03.957 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-02-20 14:48:03.961 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-02-20 14:48:03.964 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178' ;
2023-02-20 14:48:03.969 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 2 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-02-20 14:48:03.974 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-02-20 14:48:03.989 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-02-20 14:48:03.992 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179' ;
2023-02-20 14:48:03.995 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-02-20 14:48:03.999 ziyun [main] INFO  p6spy - 2023-02-20 14:48:03 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-02-20 14:48:04.007 ziyun [main] INFO  p6spy - 2023-02-20 14:48:04 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-02-20 14:48:04.019 ziyun [main] INFO  p6spy - 2023-02-20 14:48:04 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180' ;
2023-02-20 14:48:04.024 ziyun [main] INFO  p6spy - 2023-02-20 14:48:04 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-02-20 14:48:04.027 ziyun [main] INFO  p6spy - 2023-02-20 14:48:04 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-02-20 14:48:04.032 ziyun [main] INFO  p6spy - 2023-02-20 14:48:04 | 耗时 2 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-02-20 14:48:04.041 ziyun [main] INFO  p6spy - 2023-02-20 14:48:04 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181' ;
2023-02-20 14:48:04.044 ziyun [main] INFO  p6spy - 2023-02-20 14:48:04 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'phonedispose' group by u.username;
2023-02-20 14:48:04.048 ziyun [main] INFO  p6spy - 2023-02-20 14:48:04 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'phonedispose';
2023-02-20 14:48:04.051 ziyun [main] INFO  p6spy - 2023-02-20 14:48:04 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'phonedispose' and m.perms is not null and m.perms <> '';
2023-02-20 14:48:04.053 ziyun [main] INFO  p6spy - 2023-02-20 14:48:04 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='182' ;
2023-02-20 14:48:04.053 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-02-20 14:48:04.054 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-02-20 14:48:04.054 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-02-20 14:48:04.054 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-02-20 14:48:04.054 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-02-20T14:48:04.054
2023-02-20 14:48:04.284 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 14:48:04 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE TITLE = '3076';
2023-02-20 14:48:05.915 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 14:48:05 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE TITLE = '3076';
2023-02-20 14:48:26.501 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 14:48:26 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE TITLE = '3076';
2023-02-20 14:49:12.104 ziyun [Thread-3] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2023-02-20 14:49:12.106 ziyun [Thread-3] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2023-02-20 14:49:12.106 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676875680815 shutting down.
2023-02-20 14:49:12.107 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676875680815 paused.
2023-02-20 14:49:12.107 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676875680815 shutdown complete.
2023-02-20 14:49:12.110 ziyun [Thread-3] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2023-02-20 14:49:12.111 ziyun [Thread-3] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2023-02-20 14:49:12.116 ziyun [Thread-3] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
2023-02-20 14:49:24.513 ziyun [main] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on DESKTOP-EAM764B with PID 6664 (started by karl in D:\project\Java Projectes\queuingsystem)
2023-02-20 14:49:24.516 ziyun [main] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-02-20 14:49:26.040 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-02-20 14:49:26.044 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-02-20 14:49:26.150 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 91ms. Found 0 repository interfaces.
2023-02-20 14:49:26.629 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$ff94e98f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:26.815 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:26.819 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$69235a60] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:26.826 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:26.831 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:26.832 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$53cf327a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:27.322 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$93a84bce] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:27.341 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:27.346 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:27.412 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:27.421 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$8ee37560] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:27.424 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:27.432 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$3d5d5629] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:27.443 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:27.445 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:27.453 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-02-20 14:49:27.493 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-02-20 14:49:27.928 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-02-20 14:49:27.929 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-02-20 14:49:27.929 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-02-20 14:49:27.929 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-02-20 14:49:27.929 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:27.944 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:27.953 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.765 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.772 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.777 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.779 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy114] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.813 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.814 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy116] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.824 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.825 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.827 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.866 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.883 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.905 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.907 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy121] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.910 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.935 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.937 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.970 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.972 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.977 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:28.993 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.011 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$5b286920] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.015 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$d752b39] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.022 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$c88c1128] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.029 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.032 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.038 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$ef578856] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.043 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.045 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$3d3406af] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.054 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.056 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.059 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.061 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.079 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.101 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.106 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.107 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.114 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.126 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-20 14:49:29.498 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2023-02-20 14:49:29.512 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2023-02-20 14:49:29.522 ziyun [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-02-20 14:49:29.522 ziyun [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2023-02-20 14:49:29.698 ziyun [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-02-20 14:49:29.698 ziyun [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5137 ms
2023-02-20 14:49:30.370 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-02-20 14:49:30.381 ziyun [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-02-20 14:49:30.381 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-02-20 14:49:30.384 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-02-20 14:49:30.385 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-02-20 14:49:30.386 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'DESKTOP-EAM764B1676875770371'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-02-20 14:49:30.386 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-02-20 14:49:30.386 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-02-20 14:49:30.387 ziyun [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@1122853
2023-02-20 14:49:30.425 ziyun [main] INFO  p6spy - 2023-02-20 14:49:30 | 耗时 1 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-02-20 14:49:31.290 ziyun [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-02-20 14:49:31.386 ziyun [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-02-20 14:49:31.419 ziyun [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-02-20 14:49:32.147 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-02-20 14:49:32.162 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-02-20 14:49:32.199 ziyun [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-02-20 14:49:32.317 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-02-20 14:49:32.326 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-02-20 14:49:32.334 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-02-20 14:49:32.339 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-02-20 14:49:32.351 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-02-20 14:49:32.386 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-02-20 14:49:32.389 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-02-20 14:49:32.399 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-02-20 14:49:32.413 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-02-20 14:49:32.415 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-02-20 14:49:32.421 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-02-20 14:49:32.435 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_10
2023-02-20 14:49:32.462 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2023-02-20 14:49:32.479 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2023-02-20 14:49:32.481 ziyun [main] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 8.574 seconds (JVM running for 14.647)
2023-02-20 14:49:32.489 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-02-20 14:49:32.499 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-02-20 14:49:32.499 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-02-20 14:49:32.530 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-02-20 14:49:32.535 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-02-20 14:49:32.569 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-02-20 14:49:32.575 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-02-20 14:49:32.580 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166' ;
2023-02-20 14:49:32.582 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-02-20 14:49:32.585 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-02-20 14:49:32.587 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-02-20 14:49:32.588 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167' ;
2023-02-20 14:49:32.590 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-02-20 14:49:32.594 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-02-20 14:49:32.596 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-02-20 14:49:32.597 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171' ;
2023-02-20 14:49:32.599 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-02-20 14:49:32.602 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-02-20 14:49:32.604 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-02-20 14:49:32.606 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172' ;
2023-02-20 14:49:32.608 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-02-20 14:49:32.611 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-02-20 14:49:32.613 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-02-20 14:49:32.614 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174' ;
2023-02-20 14:49:32.616 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-02-20 14:49:32.619 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-02-20 14:49:32.620 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-02-20 14:49:32.622 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175' ;
2023-02-20 14:49:32.624 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-02-20 14:49:32.632 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-02-20 14:49:32.634 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-02-20 14:49:32.636 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176' ;
2023-02-20 14:49:32.638 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-02-20 14:49:32.640 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-02-20 14:49:32.642 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-02-20 14:49:32.643 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177' ;
2023-02-20 14:49:32.645 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-02-20 14:49:32.648 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-02-20 14:49:32.650 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-02-20 14:49:32.651 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178' ;
2023-02-20 14:49:32.654 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-02-20 14:49:32.657 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-02-20 14:49:32.658 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-02-20 14:49:32.660 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179' ;
2023-02-20 14:49:32.662 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-02-20 14:49:32.664 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-02-20 14:49:32.669 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-02-20 14:49:32.676 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180' ;
2023-02-20 14:49:32.680 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-02-20 14:49:32.683 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-02-20 14:49:32.685 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-02-20 14:49:32.690 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181' ;
2023-02-20 14:49:32.693 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'phonedispose' group by u.username;
2023-02-20 14:49:32.695 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'phonedispose';
2023-02-20 14:49:32.696 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'phonedispose' and m.perms is not null and m.perms <> '';
2023-02-20 14:49:32.698 ziyun [main] INFO  p6spy - 2023-02-20 14:49:32 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='182' ;
2023-02-20 14:49:32.698 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-02-20 14:49:32.698 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-02-20 14:49:32.698 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-02-20 14:49:32.698 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-02-20 14:49:32.698 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-02-20T14:49:32.698
2023-02-20 14:49:41.991 ziyun [http-nio-9528-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-02-20 14:49:41.991 ziyun [http-nio-9528-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2023-02-20 14:49:42.003 ziyun [http-nio-9528-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 12 ms
2023-02-20 14:49:42.167 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 14:49:42 | 耗时 0 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE REPAIR_ID = 3076;
2023-02-20 14:49:42.170 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 14:49:42 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID = 3076 ORDER BY REPAIR_ID DESC LIMIT 0,10;
2023-02-20 14:49:51.917 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 14:49:51 | 耗时 0 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE REPAIR_ID = 3074;
2023-02-20 14:49:51.919 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 14:49:51 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID = 3074 ORDER BY REPAIR_ID DESC LIMIT 0,10;
2023-02-20 14:49:51.920 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-20 14:49:51 | 耗时 2 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-20 14:49:51.920 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 14:49:51 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 14:49:51.923 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 14:49:51 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 14:49:51.927 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 14:49:51 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 14:49:51.928 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 14:49:51 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 14:54:52.919 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-20 14:54:52 | 耗时 0 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE REPAIR_ID = 3074;
2023-02-20 14:54:52.919 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 14:54:52 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 14:54:52.919 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 14:54:52 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 14:54:52.920 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 14:54:52 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 14:54:52.920 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-20 14:54:52 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID = 3074 ORDER BY REPAIR_ID DESC LIMIT 0,10;
2023-02-20 14:54:52.922 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 14:54:52 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 14:54:52.928 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 14:54:52 | 耗时 10 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-20 14:57:55.405 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 14:57:55 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 14:57:55.407 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-20 14:57:55 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 14:57:55.409 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-20 14:57:55 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 14:59:26.337 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 14:59:26 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 14:59:26.340 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 14:59:26 | 耗时 3 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 14:59:26.344 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 14:59:26 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:03:15.483 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:03:15 | 耗时 2 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:03:15.483 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-20 15:03:15 | 耗时 2 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:03:15.486 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:03:15 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:03:15.488 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:03:15 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:03:15.491 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:03:15 | 耗时 10 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-20 15:03:29.824 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:03:29 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-20 15:03:30.192 ziyun [http-nio-9528-exec-3] INFO  c.z.r.r.c.RepairInfoController - 发送审核短信
2023-02-20 15:03:30.196 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:03:30 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=171 ;
2023-02-20 15:03:30.209 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:03:30 | 耗时 1 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='1', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', COMPLETION_TIME='2023-02-17T17:12:26.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=171, REAL_NAME='杨友富' WHERE REPAIR_ID=3074;
2023-02-20 15:03:30.241 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:03:30 | 耗时 6 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 400, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "1" completionTime: "2023-02-17" handleCont: "平台测试" userId: "171" receipt: "0" processorId: ""', '*************', '2023-02-20T15:03:30.233+0800' );
2023-02-20 15:03:30.264 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:03:30 | 耗时 0 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE REPAIR_ID = 3074;
2023-02-20 15:03:30.266 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:03:30 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID = 3074 ORDER BY REPAIR_ID DESC LIMIT 0,10;
2023-02-20 15:03:57.484 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:03:57 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-20 15:06:35.545 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:06:35 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:06:35.547 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:06:35 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:06:35.549 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:06:35 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:06:45.099 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:06:45 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-20 15:06:45.101 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:06:45 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=171 ;
2023-02-20 15:06:45.112 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:06:45 | 耗时 9 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='2', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', COMPLETION_TIME='2023-02-17T17:12:26.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=171, REAL_NAME='杨友富', PROCESSOR_ID=171 WHERE REPAIR_ID=3074;
2023-02-20 15:06:45.128 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:06:45 | 耗时 0 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 28, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "2" completionTime: "2023-02-17" handleCont: "平台测试" userId: "171" receipt: "0" processorId: "171"', '*************', '2023-02-20T15:06:45.126+0800' );
2023-02-20 15:06:45.147 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:06:45 | 耗时 0 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE REPAIR_ID = 3074;
2023-02-20 15:06:45.149 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:06:45 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID = 3074 ORDER BY REPAIR_ID DESC LIMIT 0,10;
2023-02-20 15:06:56.521 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:06:56 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-20 15:06:56.525 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:06:56 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=171 ;
2023-02-20 15:06:56.532 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:06:56 | 耗时 1 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='3', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', PARTIAL_COMPLETION_TIME='2023-02-20T15:06:56.522+0800', COMPLETION_TIME='2023-02-17T17:12:26.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=171, REAL_NAME='杨友富', PROCESSOR_ID=171 WHERE REPAIR_ID=3074;
2023-02-20 15:06:56.558 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:06:56 | 耗时 1 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 42, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "3" completionTime: "2023-02-17" handleCont: "平台测试" userId: "171" receipt: "0" processorId: "171"', '*************', '2023-02-20T15:06:56.555+0800' );
2023-02-20 15:06:56.577 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 15:06:56 | 耗时 0 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE REPAIR_ID = 3074;
2023-02-20 15:06:56.579 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 15:06:56 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID = 3074 ORDER BY REPAIR_ID DESC LIMIT 0,10;
2023-02-20 15:07:06.081 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:07:06 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-20 15:07:51.861 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:07:51 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:07:51.863 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:07:51 | 耗时 2 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-20 15:07:51.872 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:07:51 | 耗时 9 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:07:51.872 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:07:51 | 耗时 10 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:07:51.874 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:07:51 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:09:03.933 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:09:03 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:09:03.934 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:09:03 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:09:03.937 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:09:03 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:09:06.889 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:09:06 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:09:06.889 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:09:06 | 耗时 2 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-20 15:09:06.889 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:09:06 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:09:06.890 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:09:06 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:09:06.892 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:09:06 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:09:16.461 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:09:16 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-20 15:09:16.462 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:09:16 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=171 ;
2023-02-20 15:09:16.466 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:09:16 | 耗时 1 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='4', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', PARTIAL_COMPLETION_TIME='2023-02-20T15:06:57.000+0800', COMPLETION_TIME='2023-02-20T15:09:14.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=171, REAL_NAME='杨友富', PROCESSOR_ID=171 WHERE REPAIR_ID=3074;
2023-02-20 15:09:16.490 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:09:16 | 耗时 0 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 29, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "4" completionTime: "2023-02-20 15:09:14" handleCont: "平台测试" userId: "171" receipt: "0" processorId: "171"', '*************', '2023-02-20T15:09:16.488+0800' );
2023-02-20 15:09:16.506 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 15:09:16 | 耗时 0 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE REPAIR_ID = 3074;
2023-02-20 15:09:16.507 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 15:09:16 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID = 3074 ORDER BY REPAIR_ID DESC LIMIT 0,10;
2023-02-20 15:09:32.720 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:09:32 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-20 15:09:32.724 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:09:32 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=171 ;
2023-02-20 15:09:32.730 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:09:32 | 耗时 1 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='4', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', PARTIAL_COMPLETION_TIME='2023-02-20T15:06:57.000+0800', COMPLETION_TIME='2023-02-20T15:09:14.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=171, REAL_NAME='杨友富', PROCESSOR_ID=171 WHERE REPAIR_ID=3074;
2023-02-20 15:09:32.845 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:09:32 | 耗时 1 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 22, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "5" completionTime: "2023-02-20" handleCont: "平台测试" userId: "171" receipt: "0" processorId: "171"', '*************', '2023-02-20T15:09:32.735+0800' );
2023-02-20 15:09:32.884 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:09:32 | 耗时 0 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE REPAIR_ID = 3074;
2023-02-20 15:09:32.885 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:09:32 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID = 3074 ORDER BY REPAIR_ID DESC LIMIT 0,10;
2023-02-20 15:09:35.989 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:09:35 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:09:35.991 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:09:35 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:09:52.959 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:09:52 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:09:52.961 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:09:52 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:13:22.570 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 15:13:22 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:13:22.573 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 15:13:22 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:13:22.576 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-20 15:13:22 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:13:35.028 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:13:35 | 耗时 1 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-20 15:13:35.029 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:13:35 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:13:35.029 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:13:35 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:13:35.030 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:13:35 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:13:35.032 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:13:35 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:13:51.798 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 15:13:51 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:13:51.798 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:13:51 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:13:51.798 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:13:51 | 耗时 1 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-20 15:13:51.799 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:13:51 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:13:51.801 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:13:51 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:15:37.327 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:15:37 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:15:37.328 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 15:15:37 | 耗时 1 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-20 15:15:37.328 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:15:37 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:15:37.329 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:15:37 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:15:37.331 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:15:37 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:18:12.341 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:18:12 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:18:12.341 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:18:12 | 耗时 1 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-20 15:18:12.341 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:18:12 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:18:12.343 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:18:12 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:18:12.345 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:18:12 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:18:35.486 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:18:35 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:18:35.487 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 15:18:35 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:18:35.489 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 15:18:35 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:18:44.279 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:18:44 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:18:44.281 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:18:44 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:20:24.287 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:20:24 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:20:24.288 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:20:24 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:20:24.290 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:20:24 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:20:35.555 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:20:35 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:20:35.557 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:20:35 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:20:37.907 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:20:37 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:20:37.910 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 15:20:37 | 耗时 3 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:20:37.912 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:20:37 | 耗时 2 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-20 15:20:37.912 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:20:37 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:20:37.913 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 15:20:37 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:25:48.902 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:25:48 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:25:48.904 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:25:48 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:25:48.906 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:25:48 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:25:59.002 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:25:59 | 耗时 10 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:25:59.005 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:25:59 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:26:01.838 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:26:01 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:26:01.840 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:26:01 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:26:26.940 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 15:26:26 | 耗时 2 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:26:26.942 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 15:26:26 | 耗时 2 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:26:26.942 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-20 15:26:26 | 耗时 5 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-20 15:26:26.945 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:26:26 | 耗时 3 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:26:26.949 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:26:26 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:26:31.873 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:26:31 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:26:31.873 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:26:31 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:26:31.874 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:26:31 | 耗时 1 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-20 15:26:31.875 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:26:31 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:26:31.877 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:26:31 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:26:35.075 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 15:26:35 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:26:35.075 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:26:35 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:26:35.075 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 15:26:35 | 耗时 2 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-20 15:26:35.076 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:26:35 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:26:35.078 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:26:35 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:26:40.590 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:26:40 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-20 15:26:40.835 ziyun [http-nio-9528-exec-2] INFO  c.z.r.r.c.RepairInfoController - 发送审核短信
2023-02-20 15:26:40.837 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:26:40 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=171 ;
2023-02-20 15:26:40.841 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:26:40 | 耗时 1 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='1', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', PARTIAL_COMPLETION_TIME='2023-02-20T15:06:57.000+0800', COMPLETION_TIME='2023-02-20T15:09:14.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=171, REAL_NAME='杨友富' WHERE REPAIR_ID=3074;
2023-02-20 15:26:40.854 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:26:40 | 耗时 0 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 269, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "1" completionTime: "2023-02-20" handleCont: "平台测试" userId: "171" receipt: "0" processorId: ""', '*************', '2023-02-20T15:26:40.852+0800' );
2023-02-20 15:26:40.872 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:26:40 | 耗时 0 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE REPAIR_ID = 3074;
2023-02-20 15:26:40.874 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:26:40 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID = 3074 ORDER BY REPAIR_ID DESC LIMIT 0,10;
2023-02-20 15:26:45.125 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:26:45 | 耗时 3 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-20 15:26:45.132 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:26:45 | 耗时 2 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=171 ;
2023-02-20 15:26:45.142 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:26:45 | 耗时 3 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='2', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', PARTIAL_COMPLETION_TIME='2023-02-20T15:06:57.000+0800', COMPLETION_TIME='2023-02-20T15:09:14.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=171, REAL_NAME='杨友富', PROCESSOR_ID=171 WHERE REPAIR_ID=3074;
2023-02-20 15:26:45.162 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:26:45 | 耗时 0 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 43, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "2" completionTime: "2023-02-20" handleCont: "平台测试" userId: "171" receipt: "0" processorId: "171"', '*************', '2023-02-20T15:26:45.161+0800' );
2023-02-20 15:26:45.176 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:26:45 | 耗时 0 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE REPAIR_ID = 3074;
2023-02-20 15:26:45.177 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:26:45 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID = 3074 ORDER BY REPAIR_ID DESC LIMIT 0,10;
2023-02-20 15:26:56.341 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 15:26:56 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-20 15:26:56.343 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 15:26:56 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=171 ;
2023-02-20 15:26:56.347 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 15:26:56 | 耗时 1 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='4', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', PARTIAL_COMPLETION_TIME='2023-02-20T15:06:57.000+0800', COMPLETION_TIME='2023-02-20T15:26:52.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=171, REAL_NAME='杨友富', PROCESSOR_ID=171 WHERE REPAIR_ID=3074;
2023-02-20 15:26:56.372 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 15:26:56 | 耗时 0 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 35, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "4" completionTime: "2023-02-20 15:26:52" handleCont: "平台测试" userId: "171" receipt: "0" processorId: "171"', '*************', '2023-02-20T15:26:56.369+0800' );
2023-02-20 15:26:56.387 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:26:56 | 耗时 0 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE REPAIR_ID = 3074;
2023-02-20 15:26:56.389 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:26:56 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID = 3074 ORDER BY REPAIR_ID DESC LIMIT 0,10;
2023-02-20 15:27:03.783 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 15:27:03 | 耗时 4 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:27:03.789 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 15:27:03 | 耗时 4 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:27:53.775 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:27:53 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:27:53.777 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:27:53 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:27:56.640 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:27:56 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:27:56.643 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:27:56 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:28:06.287 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:28:06 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:28:06.289 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:28:06 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:28:22.167 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 15:28:22 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:28:22.167 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-20 15:28:22 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-20 15:28:22.167 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:28:22 | 耗时 1 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-20 15:28:22.168 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:28:22 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:28:22.170 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-20 15:28:22 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:28:27.694 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:28:27 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-20 15:28:27.955 ziyun [http-nio-9528-exec-4] INFO  c.z.r.r.c.RepairInfoController - 发送审核短信
2023-02-20 15:28:27.956 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:28:27 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=171 ;
2023-02-20 15:28:27.970 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:28:27 | 耗时 11 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='1', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', PARTIAL_COMPLETION_TIME='2023-02-20T15:06:57.000+0800', COMPLETION_TIME='2023-02-20T15:26:52.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=171, REAL_NAME='杨友富' WHERE REPAIR_ID=3074;
2023-02-20 15:28:27.983 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-20 15:28:27 | 耗时 1 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 288, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "1" completionTime: "2023-02-20" handleCont: "平台测试" userId: "171" receipt: "0" processorId: ""', '*************', '2023-02-20T15:28:27.981+0800' );
2023-02-20 15:28:28.000 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:28:28 | 耗时 0 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE REPAIR_ID = 3074;
2023-02-20 15:28:28.001 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:28:28 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID = 3074 ORDER BY REPAIR_ID DESC LIMIT 0,10;
2023-02-20 15:28:38.464 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:28:38 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-20 15:28:38.467 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:28:38 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=171 ;
2023-02-20 15:28:38.472 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:28:38 | 耗时 1 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='2', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', PARTIAL_COMPLETION_TIME='2023-02-20T15:06:57.000+0800', COMPLETION_TIME='2023-02-20T15:26:52.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=171, REAL_NAME='杨友富', PROCESSOR_ID=171 WHERE REPAIR_ID=3074;
2023-02-20 15:28:38.493 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:28:38 | 耗时 0 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 28, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "2" completionTime: "2023-02-20" handleCont: "平台测试" userId: "171" receipt: "0" processorId: "171"', '*************', '2023-02-20T15:28:38.491+0800' );
2023-02-20 15:28:38.511 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:28:38 | 耗时 0 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE REPAIR_ID = 3074;
2023-02-20 15:28:38.512 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-20 15:28:38 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID = 3074 ORDER BY REPAIR_ID DESC LIMIT 0,10;
2023-02-20 15:28:46.610 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 15:28:46 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-20 15:28:46.612 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 15:28:46 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=171 ;
2023-02-20 15:28:46.614 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 15:28:46 | 耗时 0 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='3', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', PARTIAL_COMPLETION_TIME='2023-02-20T15:06:57.000+0800', COMPLETION_TIME='2023-02-20T15:26:52.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=171, REAL_NAME='杨友富', PROCESSOR_ID=171 WHERE REPAIR_ID=3074;
2023-02-20 15:28:46.633 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-20 15:28:46 | 耗时 0 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 23, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "3" completionTime: "2023-02-20 15:28:43" handleCont: "平台测试" userId: "171" receipt: "0" processorId: "171"', '*************', '2023-02-20T15:28:46.632+0800' );
2023-02-20 15:28:46.648 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:28:46 | 耗时 0 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE REPAIR_ID = 3074;
2023-02-20 15:28:46.649 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-20 15:28:46 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID = 3074 ORDER BY REPAIR_ID DESC LIMIT 0,10;
2023-02-20 15:28:52.436 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-20 15:28:52 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-20 15:28:58.167 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:28:58 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-20 15:28:58.171 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:28:58 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=171 ;
2023-02-20 15:28:58.187 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:28:58 | 耗时 11 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='4', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', PARTIAL_COMPLETION_TIME='2023-02-20T15:06:57.000+0800', COMPLETION_TIME='2023-02-20T15:28:57.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=171, REAL_NAME='杨友富', PROCESSOR_ID=171 WHERE REPAIR_ID=3074;
2023-02-20 15:28:58.198 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-20 15:28:58 | 耗时 1 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 34, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "4" completionTime: "2023-02-20 15:28:57" handleCont: "平台测试" userId: "171" receipt: "0" processorId: "171"', '*************', '2023-02-20T15:28:58.196+0800' );
2023-02-20 15:28:58.212 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:28:58 | 耗时 0 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE REPAIR_ID = 3074;
2023-02-20 15:28:58.213 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-20 15:28:58 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID = 3074 ORDER BY REPAIR_ID DESC LIMIT 0,10;
2023-02-20 15:29:12.258 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:29:12 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-20 15:29:12.262 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-20 15:29:12 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-20 15:33:44.868 ziyun [Thread-3] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2023-02-20 15:33:44.869 ziyun [Thread-3] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2023-02-20 15:33:44.870 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676875770371 shutting down.
2023-02-20 15:33:44.870 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676875770371 paused.
2023-02-20 15:33:44.870 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676875770371 shutdown complete.
2023-02-20 15:33:44.872 ziyun [Thread-3] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2023-02-20 15:33:44.872 ziyun [Thread-3] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2023-02-20 15:33:44.875 ziyun [Thread-3] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
