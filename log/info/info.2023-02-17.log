2023-02-17 15:20:51.933 ziyun [main] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on DESKTOP-EAM764B with PID 10420 (started by karl in D:\project\Java Projectes\queuingsystem)
2023-02-17 15:20:51.938 ziyun [main] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-02-17 15:20:55.329 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-02-17 15:20:55.335 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-02-17 15:20:55.465 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 110ms. Found 0 repository interfaces.
2023-02-17 15:20:56.100 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3cacf2d4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:56.274 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:56.278 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$a63b63a5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:56.286 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:56.292 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:56.292 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$90e73bbf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:56.839 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$d0c05513] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:56.867 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:56.871 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:56.948 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:56.958 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$cbfb7ea5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:56.963 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:56.971 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$7a755f6e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:56.982 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:56.984 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:56.987 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-02-17 15:20:57.045 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-02-17 15:20:57.619 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-02-17 15:20:57.619 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-02-17 15:20:57.620 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-02-17 15:20:57.620 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-02-17 15:20:57.620 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:57.638 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:57.652 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:58.938 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:58.950 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:58.961 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:58.965 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy114] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.023 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.027 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy116] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.043 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.045 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.048 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.107 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.132 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.191 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.194 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy121] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.203 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.243 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.246 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.314 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.316 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.323 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.360 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.392 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$98407265] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.399 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$4a8d347e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.405 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$5a41a6d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.419 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.424 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.435 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$2c6f919b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.446 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.453 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$7a4c0ff4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.468 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.470 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.481 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.486 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.542 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.580 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.593 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.594 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.611 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:20:59.648 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 15:21:00.388 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2023-02-17 15:21:00.429 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2023-02-17 15:21:00.494 ziyun [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-02-17 15:21:00.495 ziyun [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2023-02-17 15:21:01.754 ziyun [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-02-17 15:21:01.755 ziyun [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 9764 ms
2023-02-17 15:21:03.123 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-02-17 15:21:03.141 ziyun [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-02-17 15:21:03.141 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-02-17 15:21:03.148 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-02-17 15:21:03.150 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-02-17 15:21:03.152 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'DESKTOP-EAM764B1676618463125'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-02-17 15:21:03.152 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-02-17 15:21:03.152 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-02-17 15:21:03.155 ziyun [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@4f9467
2023-02-17 15:21:03.232 ziyun [main] INFO  p6spy - 2023-02-17 15:21:03 | 耗时 3 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-02-17 15:21:04.987 ziyun [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-02-17 15:21:05.143 ziyun [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-02-17 15:21:05.206 ziyun [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-02-17 15:21:06.306 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-02-17 15:21:06.330 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-02-17 15:21:06.381 ziyun [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-02-17 15:21:06.569 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-02-17 15:21:06.591 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-02-17 15:21:06.604 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-02-17 15:21:06.610 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-02-17 15:21:06.629 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-02-17 15:21:06.681 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-02-17 15:21:06.686 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-02-17 15:21:06.698 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-02-17 15:21:06.710 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-02-17 15:21:06.712 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-02-17 15:21:06.721 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-02-17 15:21:06.747 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_10
2023-02-17 15:21:06.785 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2023-02-17 15:21:06.810 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2023-02-17 15:21:06.813 ziyun [main] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 15.726 seconds (JVM running for 29.176)
2023-02-17 15:21:06.828 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-02-17 15:21:06.847 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-02-17 15:21:06.847 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-02-17 15:21:06.891 ziyun [main] INFO  p6spy - 2023-02-17 15:21:06 | 耗时 3 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-02-17 15:21:06.905 ziyun [main] INFO  p6spy - 2023-02-17 15:21:06 | 耗时 5 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-02-17 15:21:06.970 ziyun [main] INFO  p6spy - 2023-02-17 15:21:06 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-02-17 15:21:06.982 ziyun [main] INFO  p6spy - 2023-02-17 15:21:06 | 耗时 2 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-02-17 15:21:06.990 ziyun [main] INFO  p6spy - 2023-02-17 15:21:06 | 耗时 2 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166' ;
2023-02-17 15:21:06.995 ziyun [main] INFO  p6spy - 2023-02-17 15:21:06 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-02-17 15:21:07.093 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-02-17 15:21:07.096 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-02-17 15:21:07.100 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167' ;
2023-02-17 15:21:07.103 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-02-17 15:21:07.109 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-02-17 15:21:07.112 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-02-17 15:21:07.114 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171' ;
2023-02-17 15:21:07.117 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-02-17 15:21:07.123 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-02-17 15:21:07.127 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-02-17 15:21:07.130 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172' ;
2023-02-17 15:21:07.134 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-02-17 15:21:07.138 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-02-17 15:21:07.141 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-02-17 15:21:07.143 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174' ;
2023-02-17 15:21:07.147 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-02-17 15:21:07.151 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-02-17 15:21:07.154 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-02-17 15:21:07.156 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175' ;
2023-02-17 15:21:07.159 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-02-17 15:21:07.162 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-02-17 15:21:07.166 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-02-17 15:21:07.167 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176' ;
2023-02-17 15:21:07.170 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-02-17 15:21:07.173 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-02-17 15:21:07.176 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-02-17 15:21:07.178 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177' ;
2023-02-17 15:21:07.182 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-02-17 15:21:07.184 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-02-17 15:21:07.186 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-02-17 15:21:07.189 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178' ;
2023-02-17 15:21:07.192 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-02-17 15:21:07.196 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-02-17 15:21:07.199 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-02-17 15:21:07.201 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179' ;
2023-02-17 15:21:07.204 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-02-17 15:21:07.210 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-02-17 15:21:07.220 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-02-17 15:21:07.231 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180' ;
2023-02-17 15:21:07.237 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-02-17 15:21:07.241 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-02-17 15:21:07.248 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 2 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-02-17 15:21:07.266 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181' ;
2023-02-17 15:21:07.270 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'phonedispose' group by u.username;
2023-02-17 15:21:07.275 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'phonedispose';
2023-02-17 15:21:07.278 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'phonedispose' and m.perms is not null and m.perms <> '';
2023-02-17 15:21:07.281 ziyun [main] INFO  p6spy - 2023-02-17 15:21:07 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='182' ;
2023-02-17 15:21:07.281 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-02-17 15:21:07.282 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-02-17 15:21:07.282 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-02-17 15:21:07.282 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-02-17 15:21:07.282 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-02-17T15:21:07.282
2023-02-17 15:21:10.924 ziyun [http-nio-9528-exec-3] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-02-17 15:21:10.925 ziyun [http-nio-9528-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2023-02-17 15:21:10.944 ziyun [http-nio-9528-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 19 ms
2023-02-17 15:21:11.055 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 16 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= NOW();;
2023-02-17 15:21:11.056 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 16 ms | SQL 语句：
SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid WHERE d1.district_id = 1330000;
2023-02-17 15:21:11.063 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 5 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();;
2023-02-17 15:21:11.074 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 6 ms | SQL 语句：
SELECT DATE_FORMAT(ADD_TIME, '%d') AS xAxis, count(*) as count FROM t_repair_info WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= CURDATE() GROUP BY xAxis ORDER BY xAxis;;
2023-02-17 15:21:11.077 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 4 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE STATE_TAG = '0' and ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();;
2023-02-17 15:21:11.085 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 8 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 15:21:11.095 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 27 ms | SQL 语句：
/** 上门服务 */ (select COUNT(*) AS value, '上门处理' AS name from t_repair_info r where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and USER_ID = PROCESSOR_ID and r.STATE_TAG = 4) /** 电话指导处理 */ UNION (select COUNT(*) AS value, '电话指导处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 182 and r.STATE_TAG = 4) /** 远程处理 */ UNION (select COUNT(*) AS value, '省局平台处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 180 and r.STATE_TAG = 4) /** 代维处理 */ UNION (select COUNT(*) AS value, '代维处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 181 and r.STATE_TAG = 4) /** 未处理 */ UNION (select COUNT(*) AS value, '未处理' AS name from t_repair_info r WHERE DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.STATE_TAG >=1 and r.STATE_TAG < 4);;
2023-02-17 15:21:11.479 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 2 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 15:21:11.480 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 4 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '0';
2023-02-17 15:21:11.489 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 3 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '1';
2023-02-17 15:21:11.497 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 4 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '2';
2023-02-17 15:21:11.505 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 4 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '3';
2023-02-17 15:21:11.513 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 4 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '4';
2023-02-17 15:21:11.521 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 3 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '5';
2023-02-17 15:21:11.530 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 4 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '6';
2023-02-17 15:21:11.536 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 2 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '7';
2023-02-17 15:21:11.544 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 4 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '99';
2023-02-17 15:21:11.545 ziyun [http-nio-9528-exec-5] INFO  c.z.r.r.c.RepairInfoController - [{name=取号机, value=941}, {name=评价器, value=953}, {name=LED屏, value=358}, {name=综合屏, value=172}, {name=高速球, value=43}, {name=监控半球, value=300}, {name=拾音器, value=22}, {name=硬盘录像机, value=32}, {name=其他, value=220}]
2023-02-17 15:21:11.871 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 15:21:11 | 耗时 810 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330100) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:21:12.540 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 15:21:12 | 耗时 666 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330300) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:21:13.144 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 15:21:13 | 耗时 601 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330400) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:21:13.403 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 15:21:13 | 耗时 257 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330500) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:21:13.736 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 15:21:13 | 耗时 330 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330600) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:21:14.018 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 15:21:14 | 耗时 281 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330700) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:21:14.189 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 15:21:14 | 耗时 168 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330800) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:21:14.329 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 15:21:14 | 耗时 138 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330900) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:21:14.730 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 15:21:14 | 耗时 400 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331000) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:21:14.956 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 15:21:14 | 耗时 224 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331100) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:21:14.972 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 15:21:14 | 耗时 14 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330091) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:17.162 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 15:23:17 | 耗时 1 ms | SQL 语句：
SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid WHERE d1.district_id = 1330000;
2023-02-17 15:23:17.164 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 15:23:17 | 耗时 3 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '0';
2023-02-17 15:23:17.166 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 15:23:17 | 耗时 5 ms | SQL 语句：
SELECT DATE_FORMAT(ADD_TIME, '%d') AS xAxis, count(*) as count FROM t_repair_info WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= CURDATE() GROUP BY xAxis ORDER BY xAxis;;
2023-02-17 15:23:17.171 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 15:23:17 | 耗时 3 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '1';
2023-02-17 15:23:17.172 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 15:23:17 | 耗时 4 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= NOW();;
2023-02-17 15:23:17.177 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 15:23:17 | 耗时 3 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();;
2023-02-17 15:23:17.178 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 15:23:17 | 耗时 3 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '2';
2023-02-17 15:23:17.182 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 15:23:17 | 耗时 3 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE STATE_TAG = '0' and ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();;
2023-02-17 15:23:17.183 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 15:23:17 | 耗时 17 ms | SQL 语句：
/** 上门服务 */ (select COUNT(*) AS value, '上门处理' AS name from t_repair_info r where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and USER_ID = PROCESSOR_ID and r.STATE_TAG = 4) /** 电话指导处理 */ UNION (select COUNT(*) AS value, '电话指导处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 182 and r.STATE_TAG = 4) /** 远程处理 */ UNION (select COUNT(*) AS value, '省局平台处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 180 and r.STATE_TAG = 4) /** 代维处理 */ UNION (select COUNT(*) AS value, '代维处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 181 and r.STATE_TAG = 4) /** 未处理 */ UNION (select COUNT(*) AS value, '未处理' AS name from t_repair_info r WHERE DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.STATE_TAG >=1 and r.STATE_TAG < 4);;
2023-02-17 15:23:17.185 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 15:23:17 | 耗时 3 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '3';
2023-02-17 15:23:17.193 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 15:23:17 | 耗时 3 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '4';
2023-02-17 15:23:17.200 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 15:23:17 | 耗时 3 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '5';
2023-02-17 15:23:17.207 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 15:23:17 | 耗时 3 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '6';
2023-02-17 15:23:17.212 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 15:23:17 | 耗时 2 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '7';
2023-02-17 15:23:17.218 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 15:23:17 | 耗时 2 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '99';
2023-02-17 15:23:17.218 ziyun [http-nio-9528-exec-1] INFO  c.z.r.r.c.RepairInfoController - [{name=取号机, value=941}, {name=评价器, value=953}, {name=LED屏, value=358}, {name=综合屏, value=172}, {name=高速球, value=43}, {name=监控半球, value=300}, {name=拾音器, value=22}, {name=硬盘录像机, value=32}, {name=其他, value=220}]
2023-02-17 15:23:17.806 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 15:23:17 | 耗时 641 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330100) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:18.887 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 15:23:18 | 耗时 1078 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330300) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:19.548 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 15:23:19 | 耗时 657 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330400) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:19.831 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 15:23:19 | 耗时 281 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330500) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:20.187 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 15:23:20 | 耗时 354 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330600) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:20.538 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 15:23:20 | 耗时 348 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330700) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:20.728 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 15:23:20 | 耗时 189 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330800) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:20.921 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 15:23:20 | 耗时 191 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330900) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:21.405 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 15:23:21 | 耗时 481 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331000) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:21.649 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 15:23:21 | 耗时 243 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331100) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:21.669 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 15:23:21 | 耗时 17 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330091) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:23.252 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 1 ms | SQL 语句：
SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid WHERE d1.district_id = 1330000;
2023-02-17 15:23:23.253 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 2 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= NOW();;
2023-02-17 15:23:23.254 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 2 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 15:23:23.254 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 3 ms | SQL 语句：
SELECT DATE_FORMAT(ADD_TIME, '%d') AS xAxis, count(*) as count FROM t_repair_info WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= CURDATE() GROUP BY xAxis ORDER BY xAxis;;
2023-02-17 15:23:23.254 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 2 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '0';
2023-02-17 15:23:23.256 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 2 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();;
2023-02-17 15:23:23.258 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 8 ms | SQL 语句：
/** 上门服务 */ (select COUNT(*) AS value, '上门处理' AS name from t_repair_info r where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and USER_ID = PROCESSOR_ID and r.STATE_TAG = 4) /** 电话指导处理 */ UNION (select COUNT(*) AS value, '电话指导处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 182 and r.STATE_TAG = 4) /** 远程处理 */ UNION (select COUNT(*) AS value, '省局平台处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 180 and r.STATE_TAG = 4) /** 代维处理 */ UNION (select COUNT(*) AS value, '代维处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 181 and r.STATE_TAG = 4) /** 未处理 */ UNION (select COUNT(*) AS value, '未处理' AS name from t_repair_info r WHERE DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.STATE_TAG >=1 and r.STATE_TAG < 4);;
2023-02-17 15:23:23.258 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '1';
2023-02-17 15:23:23.260 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 2 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE STATE_TAG = '0' and ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();;
2023-02-17 15:23:23.264 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 2 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 15:23:23.264 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 2 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '2';
2023-02-17 15:23:23.267 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '3';
2023-02-17 15:23:23.271 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '4';
2023-02-17 15:23:23.274 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '5';
2023-02-17 15:23:23.278 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '6';
2023-02-17 15:23:23.282 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '7';
2023-02-17 15:23:23.285 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '99';
2023-02-17 15:23:23.285 ziyun [http-nio-9528-exec-8] INFO  c.z.r.r.c.RepairInfoController - [{name=取号机, value=941}, {name=评价器, value=953}, {name=LED屏, value=358}, {name=综合屏, value=172}, {name=高速球, value=43}, {name=监控半球, value=300}, {name=拾音器, value=22}, {name=硬盘录像机, value=32}, {name=其他, value=220}]
2023-02-17 15:23:23.622 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 15:23:23 | 耗时 368 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330100) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:24.087 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 15:23:24 | 耗时 463 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330300) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:24.583 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 15:23:24 | 耗时 494 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330400) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:24.851 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 15:23:24 | 耗时 266 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330500) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:25.185 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 15:23:25 | 耗时 332 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330600) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:25.451 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 15:23:25 | 耗时 264 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330700) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:25.630 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 15:23:25 | 耗时 177 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330800) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:25.794 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 15:23:25 | 耗时 162 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330900) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:26.325 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 15:23:26 | 耗时 520 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331000) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:26.577 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 15:23:26 | 耗时 251 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331100) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:23:26.597 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 15:23:26 | 耗时 17 ms | SQL 语句：
select COALESCE(count(STATE_TAG),0) value ,tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID,STATE_TAG,r.SWJGDM,BSDTDJXH,s.DISTRICT_ID,district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330091) all_districts on s.DISTRICT_ID = all_districts.district_id) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 15:24:53.793 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 15:24:53 | 耗时 10 ms | SQL 语句：
(select COUNT(*) AS value, '上门处理' AS name from t_repair_info r where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and USER_ID = PROCESSOR_ID and r.USER_ID = 171 and r.STATE_TAG = 4) UNION (select COUNT(*) AS value, '电话指导处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 182 and r.USER_ID = 171 and r.STATE_TAG = 4) UNION (select COUNT(*) AS value, '远程处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 180 and r.USER_ID = 171 and r.STATE_TAG = 4) UNION (select COUNT(*) AS value, '代维处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 181 and r.USER_ID = 171 and r.STATE_TAG = 4) UNION (select COUNT(*) AS value, '未处理' AS name from t_repair_info r WHERE DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.USER_ID = 171 and r.STATE_TAG >=1 and r.STATE_TAG < 4) ;;
2023-02-17 15:30:15.763 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 15:30:15 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 15:30:15.804 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 15:30:15 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 15:30:15.809 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 15:30:15 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:02:54.911 ziyun [Thread-3] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2023-02-17 16:02:54.914 ziyun [Thread-3] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2023-02-17 16:02:54.914 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676618463125 shutting down.
2023-02-17 16:02:54.915 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676618463125 paused.
2023-02-17 16:02:54.915 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676618463125 shutdown complete.
2023-02-17 16:02:54.920 ziyun [Thread-3] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2023-02-17 16:02:54.921 ziyun [Thread-3] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2023-02-17 16:02:54.927 ziyun [Thread-3] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
2023-02-17 16:03:07.504 ziyun [main] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on DESKTOP-EAM764B with PID 7328 (started by karl in D:\project\Java Projectes\queuingsystem)
2023-02-17 16:03:07.507 ziyun [main] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-02-17 16:03:11.001 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-02-17 16:03:11.003 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-02-17 16:03:11.123 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 102ms. Found 0 repository interfaces.
2023-02-17 16:03:11.786 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$f46c6f35] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:12.006 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:12.011 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$5dfae006] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:12.021 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:12.027 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:12.028 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$48a6b820] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:12.610 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$887fd174] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:12.629 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:12.633 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:12.711 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:12.721 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$83bafb06] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:12.725 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:12.733 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$3234dbcf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:12.743 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:12.745 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:12.748 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-02-17 16:03:12.797 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-02-17 16:03:13.222 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-02-17 16:03:13.222 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-02-17 16:03:13.222 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-02-17 16:03:13.222 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-02-17 16:03:13.222 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:13.241 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:13.250 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.275 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.284 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.288 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.291 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy114] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.329 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.331 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy116] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.341 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.343 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.345 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.391 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.423 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.462 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.464 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy121] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.469 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.498 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.499 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.549 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.551 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.557 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.577 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.600 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$4fffeec6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.605 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$24cb0df] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.609 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$bd6396ce] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.619 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.623 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.630 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$e42f0dfc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.636 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.645 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$320b8c55] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.658 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.660 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.665 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.668 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.699 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.714 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.720 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.721 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.729 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:14.751 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-02-17 16:03:15.201 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2023-02-17 16:03:15.216 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2023-02-17 16:03:15.226 ziyun [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-02-17 16:03:15.226 ziyun [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2023-02-17 16:03:15.421 ziyun [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-02-17 16:03:15.421 ziyun [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7858 ms
2023-02-17 16:03:16.216 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-02-17 16:03:16.228 ziyun [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-02-17 16:03:16.228 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-02-17 16:03:16.231 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-02-17 16:03:16.233 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-02-17 16:03:16.233 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'DESKTOP-EAM764B1676620996217'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-02-17 16:03:16.233 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-02-17 16:03:16.233 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-02-17 16:03:16.235 ziyun [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@13c5ecd
2023-02-17 16:03:16.286 ziyun [main] INFO  p6spy - 2023-02-17 16:03:16 | 耗时 1 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-02-17 16:03:17.433 ziyun [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-02-17 16:03:17.557 ziyun [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-02-17 16:03:17.600 ziyun [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-02-17 16:03:18.518 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-02-17 16:03:18.538 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-02-17 16:03:18.586 ziyun [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-02-17 16:03:18.744 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-02-17 16:03:18.757 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-02-17 16:03:18.768 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-02-17 16:03:18.774 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-02-17 16:03:18.791 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-02-17 16:03:18.843 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-02-17 16:03:18.848 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-02-17 16:03:18.859 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-02-17 16:03:18.869 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-02-17 16:03:18.871 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-02-17 16:03:18.879 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-02-17 16:03:18.898 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_10
2023-02-17 16:03:18.931 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2023-02-17 16:03:18.955 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2023-02-17 16:03:18.959 ziyun [main] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 12.232 seconds (JVM running for 18.292)
2023-02-17 16:03:18.971 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-02-17 16:03:18.986 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-02-17 16:03:18.986 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-02-17 16:03:19.032 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-02-17 16:03:19.043 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 4 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-02-17 16:03:19.095 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-02-17 16:03:19.103 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-02-17 16:03:19.120 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166' ;
2023-02-17 16:03:19.123 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-02-17 16:03:19.126 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-02-17 16:03:19.129 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-02-17 16:03:19.130 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167' ;
2023-02-17 16:03:19.133 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-02-17 16:03:19.137 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-02-17 16:03:19.139 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-02-17 16:03:19.141 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171' ;
2023-02-17 16:03:19.143 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-02-17 16:03:19.147 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-02-17 16:03:19.150 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-02-17 16:03:19.152 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172' ;
2023-02-17 16:03:19.154 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-02-17 16:03:19.158 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-02-17 16:03:19.161 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-02-17 16:03:19.163 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174' ;
2023-02-17 16:03:19.166 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-02-17 16:03:19.170 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-02-17 16:03:19.173 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-02-17 16:03:19.176 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175' ;
2023-02-17 16:03:19.179 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-02-17 16:03:19.182 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-02-17 16:03:19.186 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-02-17 16:03:19.188 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176' ;
2023-02-17 16:03:19.191 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-02-17 16:03:19.194 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-02-17 16:03:19.197 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-02-17 16:03:19.199 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177' ;
2023-02-17 16:03:19.203 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-02-17 16:03:19.206 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-02-17 16:03:19.208 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-02-17 16:03:19.211 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178' ;
2023-02-17 16:03:19.213 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-02-17 16:03:19.217 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-02-17 16:03:19.220 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-02-17 16:03:19.223 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179' ;
2023-02-17 16:03:19.226 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-02-17 16:03:19.230 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-02-17 16:03:19.239 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-02-17 16:03:19.250 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180' ;
2023-02-17 16:03:19.255 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-02-17 16:03:19.258 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-02-17 16:03:19.263 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 2 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-02-17 16:03:19.271 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181' ;
2023-02-17 16:03:19.274 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 1 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'phonedispose' group by u.username;
2023-02-17 16:03:19.276 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'phonedispose';
2023-02-17 16:03:19.278 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'phonedispose' and m.perms is not null and m.perms <> '';
2023-02-17 16:03:19.280 ziyun [main] INFO  p6spy - 2023-02-17 16:03:19 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='182' ;
2023-02-17 16:03:19.280 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-02-17 16:03:19.280 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-02-17 16:03:19.280 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-02-17 16:03:19.280 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-02-17 16:03:19.281 ziyun [main] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-02-17T16:03:19.281
2023-02-17 16:03:48.428 ziyun [http-nio-9528-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-02-17 16:03:48.428 ziyun [http-nio-9528-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2023-02-17 16:03:48.445 ziyun [http-nio-9528-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 17 ms
2023-02-17 16:03:48.514 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:03:48 | 耗时 2 ms | SQL 语句：
SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid WHERE d1.district_id = 1330000;
2023-02-17 16:03:48.515 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:03:48 | 耗时 4 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= NOW();;
2023-02-17 16:03:48.518 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:03:48 | 耗时 2 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();;
2023-02-17 16:03:48.522 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:03:48 | 耗时 5 ms | SQL 语句：
SELECT DATE_FORMAT(ADD_TIME, '%d') AS xAxis, count(*) as count FROM t_repair_info WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= CURDATE() GROUP BY xAxis ORDER BY xAxis;;
2023-02-17 16:03:48.523 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:03:48 | 耗时 3 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE STATE_TAG = '0' and ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();;
2023-02-17 16:03:48.526 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 16:03:48 | 耗时 4 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 16:03:48.532 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:03:48 | 耗时 15 ms | SQL 语句：
/** 上门服务 */ (select COUNT(*) AS value, '上门处理' AS name from t_repair_info r where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and USER_ID = PROCESSOR_ID and r.STATE_TAG = 4) /** 电话指导处理 */ UNION (select COUNT(*) AS value, '电话指导处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 182 and r.STATE_TAG = 4) /** 远程处理 */ UNION (select COUNT(*) AS value, '省局平台处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 180 and r.STATE_TAG = 4) /** 代维处理 */ UNION (select COUNT(*) AS value, '代维处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 181 and r.STATE_TAG = 4) /** 未处理 */ UNION (select COUNT(*) AS value, '未处理' AS name from t_repair_info r WHERE DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.STATE_TAG >=1 and r.STATE_TAG < 4);;
2023-02-17 16:03:48.658 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 16:03:48 | 耗时 3 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:03:48.659 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:03:48 | 耗时 5 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '0';
2023-02-17 16:03:48.667 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:03:48 | 耗时 3 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '1';
2023-02-17 16:03:48.676 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:03:48 | 耗时 3 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '2';
2023-02-17 16:03:48.685 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:03:48 | 耗时 4 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '3';
2023-02-17 16:03:48.691 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:03:48 | 耗时 2 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '4';
2023-02-17 16:03:48.698 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:03:48 | 耗时 2 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '5';
2023-02-17 16:03:48.705 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:03:48 | 耗时 2 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '6';
2023-02-17 16:03:48.712 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:03:48 | 耗时 4 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '7';
2023-02-17 16:03:48.720 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:03:48 | 耗时 3 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '99';
2023-02-17 16:03:48.720 ziyun [http-nio-9528-exec-2] INFO  c.z.r.r.c.RepairInfoController - [{name=取号机, value=941}, {name=评价器, value=953}, {name=LED屏, value=358}, {name=综合屏, value=172}, {name=高速球, value=43}, {name=监控半球, value=300}, {name=拾音器, value=22}, {name=硬盘录像机, value=32}, {name=其他, value=220}]
2023-02-17 16:03:49.264 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:03:49 | 耗时 746 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330100) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:49.944 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:03:49 | 耗时 678 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330300) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:50.544 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:03:50 | 耗时 597 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330400) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:50.807 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:03:50 | 耗时 261 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330500) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:51.142 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:03:51 | 耗时 333 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330600) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:51.415 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:03:51 | 耗时 272 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330700) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:51.572 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:03:51 | 耗时 155 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330800) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:51.708 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:03:51 | 耗时 134 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330900) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:52.113 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:03:52 | 耗时 404 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331000) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:52.318 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:03:52 | 耗时 203 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331100) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:52.334 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:03:52 | 耗时 13 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330091) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:53.266 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:03:53 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:03:53.346 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 16:03:53 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:03:53.358 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 16:03:53 | 耗时 3 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:03:55.509 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 16:03:55 | 耗时 4 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= NOW();;
2023-02-17 16:03:55.509 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:03:55 | 耗时 3 ms | SQL 语句：
SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid WHERE d1.district_id = 1330000;
2023-02-17 16:03:55.510 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:03:55 | 耗时 4 ms | SQL 语句：
SELECT DATE_FORMAT(ADD_TIME, '%d') AS xAxis, count(*) as count FROM t_repair_info WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= CURDATE() GROUP BY xAxis ORDER BY xAxis;;
2023-02-17 16:03:55.512 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 16:03:55 | 耗时 5 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 16:03:55.514 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:03:55 | 耗时 3 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '0';
2023-02-17 16:03:55.515 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 16:03:55 | 耗时 3 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();;
2023-02-17 16:03:55.524 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 16:03:55 | 耗时 6 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE STATE_TAG = '0' and ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();;
2023-02-17 16:03:55.527 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:03:55 | 耗时 7 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '1';
2023-02-17 16:03:55.527 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 16:03:55 | 耗时 19 ms | SQL 语句：
/** 上门服务 */ (select COUNT(*) AS value, '上门处理' AS name from t_repair_info r where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and USER_ID = PROCESSOR_ID and r.STATE_TAG = 4) /** 电话指导处理 */ UNION (select COUNT(*) AS value, '电话指导处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 182 and r.STATE_TAG = 4) /** 远程处理 */ UNION (select COUNT(*) AS value, '省局平台处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 180 and r.STATE_TAG = 4) /** 代维处理 */ UNION (select COUNT(*) AS value, '代维处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 181 and r.STATE_TAG = 4) /** 未处理 */ UNION (select COUNT(*) AS value, '未处理' AS name from t_repair_info r WHERE DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.STATE_TAG >=1 and r.STATE_TAG < 4);;
2023-02-17 16:03:55.535 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:03:55 | 耗时 3 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '2';
2023-02-17 16:03:55.543 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:03:55 | 耗时 3 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '3';
2023-02-17 16:03:55.549 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:03:55 | 耗时 2 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '4';
2023-02-17 16:03:55.549 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:03:55 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:03:55.555 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:03:55 | 耗时 3 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '5';
2023-02-17 16:03:55.561 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:03:55 | 耗时 2 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '6';
2023-02-17 16:03:55.569 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:03:55 | 耗时 4 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '7';
2023-02-17 16:03:55.580 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:03:55 | 耗时 3 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '99';
2023-02-17 16:03:55.580 ziyun [http-nio-9528-exec-1] INFO  c.z.r.r.c.RepairInfoController - [{name=取号机, value=941}, {name=评价器, value=953}, {name=LED屏, value=358}, {name=综合屏, value=172}, {name=高速球, value=43}, {name=监控半球, value=300}, {name=拾音器, value=22}, {name=硬盘录像机, value=32}, {name=其他, value=220}]
2023-02-17 16:03:56.185 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:03:56 | 耗时 673 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330100) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:56.896 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:03:56 | 耗时 709 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330300) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:57.405 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:03:57 | 耗时 507 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330400) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:57.687 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:03:57 | 耗时 280 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330500) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:58.068 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:03:58 | 耗时 377 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330600) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:58.370 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:03:58 | 耗时 300 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330700) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:58.545 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:03:58 | 耗时 173 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330800) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:58.689 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:03:58 | 耗时 141 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330900) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:59.137 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:03:59 | 耗时 447 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331000) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:59.359 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:03:59 | 耗时 219 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331100) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:03:59.374 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:03:59 | 耗时 14 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330091) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:04:59.556 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 1 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= NOW();;
2023-02-17 16:04:59.556 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 0 ms | SQL 语句：
SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid WHERE d1.district_id = 1330000;
2023-02-17 16:04:59.558 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 1 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();;
2023-02-17 16:04:59.558 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 1 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 16:04:59.558 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 2 ms | SQL 语句：
SELECT DATE_FORMAT(ADD_TIME, '%d') AS xAxis, count(*) as count FROM t_repair_info WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= CURDATE() GROUP BY xAxis ORDER BY xAxis;;
2023-02-17 16:04:59.558 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '0';
2023-02-17 16:04:59.561 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 1 ms | SQL 语句：
SELECT COUNT(*) as count FROM t_repair_info WHERE STATE_TAG = '0' and ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();;
2023-02-17 16:04:59.562 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 2 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '1';
2023-02-17 16:04:59.565 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 9 ms | SQL 语句：
/** 上门服务 */ (select COUNT(*) AS value, '上门处理' AS name from t_repair_info r where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and USER_ID = PROCESSOR_ID and r.STATE_TAG = 4) /** 电话指导处理 */ UNION (select COUNT(*) AS value, '电话指导处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 182 and r.STATE_TAG = 4) /** 远程处理 */ UNION (select COUNT(*) AS value, '省局平台处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 180 and r.STATE_TAG = 4) /** 代维处理 */ UNION (select COUNT(*) AS value, '代维处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.PROCESSOR_ID = 181 and r.STATE_TAG = 4) /** 未处理 */ UNION (select COUNT(*) AS value, '未处理' AS name from t_repair_info r WHERE DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.STATE_TAG >=1 and r.STATE_TAG < 4);;
2023-02-17 16:04:59.566 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '2';
2023-02-17 16:04:59.566 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:04:59.569 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '3';
2023-02-17 16:04:59.572 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '4';
2023-02-17 16:04:59.575 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '5';
2023-02-17 16:04:59.578 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '6';
2023-02-17 16:04:59.582 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '7';
2023-02-17 16:04:59.585 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 1 ms | SQL 语句：
SELECT COUNT( 1 ) FROM t_repair_info WHERE TITLE = '99';
2023-02-17 16:04:59.585 ziyun [http-nio-9528-exec-1] INFO  c.z.r.r.c.RepairInfoController - [{name=取号机, value=941}, {name=评价器, value=953}, {name=LED屏, value=358}, {name=综合屏, value=172}, {name=高速球, value=43}, {name=监控半球, value=300}, {name=拾音器, value=22}, {name=硬盘录像机, value=32}, {name=其他, value=220}]
2023-02-17 16:04:59.963 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:04:59 | 耗时 405 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330100) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:05:00.459 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:05:00 | 耗时 495 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330300) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:05:00.946 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:05:00 | 耗时 485 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330400) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:05:01.212 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:05:01 | 耗时 265 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330500) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:05:01.554 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:05:01 | 耗时 340 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330600) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:05:01.855 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:05:01 | 耗时 299 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330700) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:05:02.039 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:05:02 | 耗时 181 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330800) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:05:02.202 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:05:02 | 耗时 161 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330900) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:05:02.663 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:05:02 | 耗时 460 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331000) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:05:02.915 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:05:02 | 耗时 250 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331100) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:05:02.933 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:05:02 | 耗时 16 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330091) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-02-17 16:05:42.717 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 16:05:42 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:05:42.719 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 16:05:42 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:05:42.721 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 16:05:42 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:08:54.334 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:08:54 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:08:54.334 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:08:54 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:08:54.336 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:08:54 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:13:40.920 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:13:40 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:13:40.921 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:13:40 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:13:40.923 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:13:40 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:19:25.297 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:19:25 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:19:25.298 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 16:19:25 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:19:25.300 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 16:19:25 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:20:18.062 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:20:18 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:20:18.063 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:20:18 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:20:18.065 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:20:18 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:20:51.908 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 16:20:51 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:20:51.909 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 16:20:51 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:20:51.911 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 16:20:51 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:21:38.476 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:21:38 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:21:38.480 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:21:38 | 耗时 3 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:21:38.483 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:21:38 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:22:13.864 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 16:22:13 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:22:13.865 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:22:13 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:22:13.869 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:22:13 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:25:12.374 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 16:25:12 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:25:12.376 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 16:25:12 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:25:12.378 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 16:25:12 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:25:50.975 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:25:50 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:25:50.977 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:25:50 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:25:50.979 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:25:50 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:26:03.177 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:26:03 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:26:03.178 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:26:03 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:26:03.178 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 16:26:03 | 耗时 1 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 16:26:03.179 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 16:26:03 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:26:03.180 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 16:26:03 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:26:08.238 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:26:08 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:26:08.238 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 16:26:08 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:26:08.239 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 16:26:08 | 耗时 1 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 16:26:08.240 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:26:08 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:26:08.242 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:26:08 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:26:34.548 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:26:34 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:26:34.549 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:26:34 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:26:34.550 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:26:34 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:26:58.448 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:26:58 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:26:58.449 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:26:58 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:26:58.451 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:26:58 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:27:28.477 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 16:27:28 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:27:28.479 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:27:28 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:27:28.482 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:27:28 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:27:55.389 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:27:55 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:27:55.395 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 16:27:55 | 耗时 5 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:27:55.397 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 16:27:55 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:28:55.327 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:28:55 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:28:55.327 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:28:55 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:28:55.328 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:28:55 | 耗时 1 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 16:28:55.330 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:28:55 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:28:55.332 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:28:55 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:29:11.391 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 16:29:11 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:29:11.391 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:29:11 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:29:11.391 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 16:29:11 | 耗时 1 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 16:29:11.393 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 16:29:11 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:29:11.396 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 16:29:11 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:29:21.803 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 16:29:21 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:29:21.805 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:29:21 | 耗时 1 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 16:29:21.805 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:29:21 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:29:21.805 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:29:21 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:29:21.807 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:29:21 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:29:48.232 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:29:48 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:29:48.232 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 16:29:48 | 耗时 2 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 16:29:48.232 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:29:48 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:29:48.234 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:29:48 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:29:48.236 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:29:48 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:34:55.810 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:34:55 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:34:55.811 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:34:55 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:34:55.813 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:34:55 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:35:43.063 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:35:43 | 耗时 2 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:35:43.064 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:35:43 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:35:43.066 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 16:35:43 | 耗时 2 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 16:35:43.067 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:35:43 | 耗时 2 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:35:43.067 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:35:43 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:35:49.154 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:35:49 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-17 16:35:49.710 ziyun [http-nio-9528-exec-3] INFO  c.z.r.r.c.RepairInfoController - 发送审核短信
2023-02-17 16:35:49.718 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:35:49 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=171 ;
2023-02-17 16:35:49.727 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:35:49 | 耗时 1 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='1', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', COMPLETION_TIME='2023-02-16T00:00:00.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=171, REAL_NAME='杨友富' WHERE REPAIR_ID=3074;
2023-02-17 16:35:49.749 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:35:49 | 耗时 3 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 584, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "1" completionTime: "2023-02-16" handleCont: "平台测试" userId: "171" receipt: "0" processorId: ""', '*************', '2023-02-17T16:35:49.744+0800' );
2023-02-17 16:35:49.769 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 16:35:49 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:35:49.771 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 16:35:49 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:35:57.792 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:35:57 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-17 16:35:57.795 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:35:57 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=171 ;
2023-02-17 16:35:57.799 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:35:57 | 耗时 1 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='2', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', COMPLETION_TIME='2023-02-16T00:00:00.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=171, REAL_NAME='杨友富', PROCESSOR_ID=171 WHERE REPAIR_ID=3074;
2023-02-17 16:35:57.813 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:35:57 | 耗时 0 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 18, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "2" completionTime: "2023-02-16" handleCont: "平台测试" userId: "171" receipt: "0" processorId: "171"', '*************', '2023-02-17T16:35:57.810+0800' );
2023-02-17 16:35:57.831 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:35:57 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:35:57.834 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:35:57 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:38:46.745 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:38:46 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:38:46.746 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 16:38:46 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:38:46.748 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 16:38:46 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:40:39.717 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:40:39 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:40:39.718 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:40:39 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:40:39.720 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:40:39 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:40:50.427 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 16:40:50 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-17 16:41:44.781 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:41:44 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-17 16:41:44.782 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:41:44 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=171 ;
2023-02-17 16:41:44.786 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:41:44 | 耗时 2 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='4', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', COMPLETION_TIME='2023-02-17T16:41:42.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=171, REAL_NAME='杨友富', PROCESSOR_ID=171 WHERE REPAIR_ID=3074;
2023-02-17 16:41:44.801 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:41:44 | 耗时 0 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 18, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "4" completionTime: "2023-02-17 16:41:42" handleCont: "平台测试" userId: "171" receipt: "0" processorId: "171"', '*************', '2023-02-17T16:41:44.798+0800' );
2023-02-17 16:41:44.817 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 16:41:44 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:41:44.818 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 16:41:44 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:45:25.975 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:45:25 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:45:25.977 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 16:45:25 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:45:25.979 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 16:45:25 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:46:28.349 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:46:28 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:46:28.350 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 16:46:28 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:46:28.352 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 16:46:28 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:46:45.401 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:46:45 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:46:45.402 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:46:45 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:46:45.405 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:46:45 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:48:09.969 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 16:48:09 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:48:09.971 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:48:09 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:48:09.973 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:48:09 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:57:30.410 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:57:30 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:57:30.410 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 16:57:30 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:57:30.412 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 16:57:30 | 耗时 3 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 16:57:30.412 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:57:30 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:57:30.415 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 16:57:30 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:57:31.754 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 16:57:31 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:57:31.758 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 16:57:31 | 耗时 3 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:57:31.759 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 16:57:31 | 耗时 2 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 16:57:31.760 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 16:57:31 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:57:31.762 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 16:57:31 | 耗时 3 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 16:57:54.420 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 16:57:54 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:57:54.422 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:57:54 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 16:57:54.423 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 16:57:54 | 耗时 1 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 16:57:54.424 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 16:57:54 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 16:57:54.424 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 16:57:54 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:00:24.471 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 17:00:24 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:00:24.472 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 17:00:24 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:00:24.472 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 17:00:24 | 耗时 1 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 17:00:24.473 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 17:00:24 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:00:24.475 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 17:00:24 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:00:37.093 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 17:00:37 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:00:37.093 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 17:00:37 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:00:37.093 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 17:00:37 | 耗时 1 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 17:00:37.094 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 17:00:37 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:00:37.096 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 17:00:37 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:04:45.834 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 17:04:45 | 耗时 2 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:04:45.841 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 17:04:45 | 耗时 5 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:04:45.841 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 17:04:45 | 耗时 5 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 17:04:45.843 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 17:04:45 | 耗时 2 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:04:45.845 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 17:04:45 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:05:00.975 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 17:05:00 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:05:00.979 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 17:05:00 | 耗时 3 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:05:00.983 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 17:05:00 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:05:01.052 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 17:05:01 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:05:01.055 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 17:05:01 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:05:01.058 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 17:05:01 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:05:05.801 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 17:05:05 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:05:05.803 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 17:05:05 | 耗时 2 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 17:05:05.804 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 17:05:05 | 耗时 3 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:05:05.804 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 17:05:05 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:05:05.809 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 17:05:05 | 耗时 3 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:05:15.468 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 17:05:15 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-17 17:05:15.753 ziyun [http-nio-9528-exec-5] INFO  c.z.r.r.c.RepairInfoController - 发送审核短信
2023-02-17 17:05:15.756 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 17:05:15 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=178 ;
2023-02-17 17:05:15.760 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 17:05:15 | 耗时 1 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='1', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', COMPLETION_TIME='2023-02-17T16:41:42.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=178, REAL_NAME='徐小渭' WHERE REPAIR_ID=3074;
2023-02-17 17:05:15.772 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 17:05:15 | 耗时 1 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 302, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "1" completionTime: "2023-02-17" handleCont: "平台测试" userId: "178" receipt: "0" processorId: ""', '*************', '2023-02-17T17:05:15.769+0800' );
2023-02-17 17:05:15.795 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 17:05:15 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:05:15.798 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 17:05:15 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:05:52.128 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 17:05:52 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-17 17:05:52.324 ziyun [http-nio-9528-exec-3] INFO  c.z.r.r.c.RepairInfoController - 发送审核短信
2023-02-17 17:05:52.326 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 17:05:52 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=178 ;
2023-02-17 17:05:52.330 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 17:05:52 | 耗时 1 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='1', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', COMPLETION_TIME='2023-02-17T16:41:42.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=178, REAL_NAME='徐小渭' WHERE REPAIR_ID=3074;
2023-02-17 17:05:52.336 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 17:05:52 | 耗时 1 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 207, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "1" completionTime: "2023-02-17" handleCont: "平台测试" userId: "178" receipt: "0" processorId: ""', '*************', '2023-02-17T17:05:52.333+0800' );
2023-02-17 17:05:52.372 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 17:05:52 | 耗时 3 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:05:52.377 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 17:05:52 | 耗时 3 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:06:06.356 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 17:06:06 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-17 17:06:06.526 ziyun [http-nio-9528-exec-5] INFO  c.z.r.r.c.RepairInfoController - 发送审核短信
2023-02-17 17:06:06.528 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 17:06:06 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=178 ;
2023-02-17 17:06:06.532 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 17:06:06 | 耗时 0 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='1', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', COMPLETION_TIME='2023-02-17T16:41:42.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=178, REAL_NAME='徐小渭' WHERE REPAIR_ID=3074;
2023-02-17 17:06:06.538 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 17:06:06 | 耗时 0 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 179, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "1" completionTime: "2023-02-17" handleCont: "平台测试" userId: "178" receipt: "0" processorId: ""', '*************', '2023-02-17T17:06:06.534+0800' );
2023-02-17 17:06:06.559 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 17:06:06 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:06:06.563 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 17:06:06 | 耗时 3 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:06:12.029 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 17:06:12 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-17 17:06:12.201 ziyun [http-nio-9528-exec-7] INFO  c.z.r.r.c.RepairInfoController - 发送审核短信
2023-02-17 17:06:12.203 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 17:06:12 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=174 ;
2023-02-17 17:06:12.206 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 17:06:12 | 耗时 0 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='1', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', COMPLETION_TIME='2023-02-17T16:41:42.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=174, REAL_NAME='王剑勇' WHERE REPAIR_ID=3074;
2023-02-17 17:06:12.217 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 17:06:12 | 耗时 0 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 187, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "1" completionTime: "2023-02-17" handleCont: "平台测试" userId: "174" receipt: "0" processorId: ""', '*************', '2023-02-17T17:06:12.215+0800' );
2023-02-17 17:06:12.235 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 17:06:12 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:06:12.237 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 17:06:12 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:09:19.303 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 17:09:19 | 耗时 2 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:09:19.307 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 17:09:19 | 耗时 4 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:09:19.308 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 17:09:19 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:09:19.308 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 17:09:19 | 耗时 3 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 17:09:19.312 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 17:09:19 | 耗时 3 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:09:35.191 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 17:09:35 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:09:35.194 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 17:09:35 | 耗时 3 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-02-17 17:09:35.195 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 17:09:35 | 耗时 3 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:09:35.195 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 17:09:35 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:09:35.201 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 17:09:35 | 耗时 4 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:09:41.798 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 17:09:41 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-17 17:09:42.099 ziyun [http-nio-9528-exec-4] INFO  c.z.r.r.c.RepairInfoController - 发送审核短信
2023-02-17 17:09:42.100 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 17:09:42 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=176 ;
2023-02-17 17:09:42.103 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 17:09:42 | 耗时 1 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='1', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', COMPLETION_TIME='2023-02-17T16:41:42.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=176, REAL_NAME='毕诚诚' WHERE REPAIR_ID=3074;
2023-02-17 17:09:42.114 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 17:09:42 | 耗时 1 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 315, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "1" completionTime: "2023-02-17" handleCont: "平台测试" userId: "176" receipt: "0" processorId: ""', '*************', '2023-02-17T17:09:42.111+0800' );
2023-02-17 17:09:42.136 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 17:09:42 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:09:42.139 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 17:09:42 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:10:01.642 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 17:10:01 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:10:01.643 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 17:10:01 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:10:01.647 ziyun [http-nio-9528-exec-5] INFO  p6spy - 2023-02-17 17:10:01 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:10:01.722 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-02-17 17:10:01 | 耗时 1 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:10:01.724 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 17:10:01 | 耗时 2 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:10:01.727 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-02-17 17:10:01 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:10:14.283 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 17:10:14 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-17 17:10:14.286 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 17:10:14 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=176 ;
2023-02-17 17:10:14.290 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 17:10:14 | 耗时 1 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='2', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', COMPLETION_TIME='2023-02-17T16:41:42.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=176, REAL_NAME='毕诚诚', PROCESSOR_ID=171 WHERE REPAIR_ID=3074;
2023-02-17 17:10:14.301 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 17:10:14 | 耗时 0 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 16, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "2" completionTime: "2023-02-17" handleCont: "平台测试" userId: "176" receipt: "0" processorId: "171"', '*************', '2023-02-17T17:10:14.299+0800' );
2023-02-17 17:10:14.320 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 17:10:14 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:10:14.322 ziyun [http-nio-9528-exec-8] INFO  p6spy - 2023-02-17 17:10:14 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:12:38.361 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 17:12:38 | 耗时 0 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE REPAIR_ID=3074 ;
2023-02-17 17:12:38.371 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 17:12:38 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE USER_ID=176 ;
2023-02-17 17:12:38.374 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 17:12:38 | 耗时 1 ms | SQL 语句：
UPDATE t_repair_info SET NAME='小老毕', MOBILE='13757125721', TITLE='5', ADDRESS='建国南路42号', STATE_TAG='4', REPAIR_NUMBER='20230216165559659', ADD_TIME='2023-02-16T16:56:00.000+0800', REVIEW_TIME='2023-02-16T17:50:09.000+0800', DEAL_TIME='2023-02-16T17:59:36.000+0800', COMPLETION_TIME='2023-02-17T17:12:26.000+0800', CONT='111', HANDLE_CONT='平台测试', RECEIPT=0, SWJGDM='1330000116', BSDTMC='国家税务总局杭州市上城区税务局建国南路办税服务厅-建国南路办税厅', USER_ID=176, REAL_NAME='毕诚诚', PROCESSOR_ID=171 WHERE REPAIR_ID=3074;
2023-02-17 17:12:38.386 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-02-17 17:12:38 | 耗时 1 ms | SQL 语句：
INSERT INTO t_log ( username, operation, time, method, params, ip, create_time ) VALUES ( '', '修改工单', 23, 'com.ziyun.repairsystem.repair.controller.RepairInfoController.updateRepair()', ' repairId: "3074" stateTag: "4" completionTime: "2023-02-17 17:12:26" handleCont: "平台测试" userId: "176" receipt: "0" processorId: "171"', '*************', '2023-02-17T17:12:38.383+0800' );
2023-02-17 17:12:38.410 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 17:12:38 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:12:38.413 ziyun [http-nio-9528-exec-7] INFO  p6spy - 2023-02-17 17:12:38 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:14:52.934 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-02-17 17:14:52 | 耗时 3 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:14:52.936 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 17:14:52 | 耗时 3 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:14:52.939 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-02-17 17:14:52 | 耗时 2 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:14:52.989 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-02-17 17:14:52 | 耗时 0 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE dept_id = '2';
2023-02-17 17:14:52.992 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 17:14:52 | 耗时 1 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-02-17 17:14:52.994 ziyun [http-nio-9528-exec-10] INFO  p6spy - 2023-02-17 17:14:52 | 耗时 1 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID FROM t_repair_info WHERE ADD_TIME >= '1900-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-02-17 17:23:20.016 ziyun [Thread-3] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2023-02-17 17:23:20.019 ziyun [Thread-3] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2023-02-17 17:23:20.019 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676620996217 shutting down.
2023-02-17 17:23:20.019 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676620996217 paused.
2023-02-17 17:23:20.020 ziyun [Thread-3] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1676620996217 shutdown complete.
2023-02-17 17:23:20.024 ziyun [Thread-3] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2023-02-17 17:23:20.025 ziyun [Thread-3] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2023-02-17 17:23:20.028 ziyun [Thread-3] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
