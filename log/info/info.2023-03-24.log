2023-03-24 02:12:02.359 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on DESKTOP-EAM764B with PID 63240 (started by karl in D:\project\Java Projectes\queuingsystem)
2023-03-24 02:12:02.363 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-03-24 02:12:02.438 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2023-03-24 02:12:02.439 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2023-03-24 02:12:05.945 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-03-24 02:12:05.948 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-03-24 02:12:06.092 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 124ms. Found 0 repository interfaces.
2023-03-24 02:12:06.655 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$1470a830] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:06.814 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:06.818 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$7dff1901] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:06.826 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:06.831 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:06.832 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$68aaf11b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:07.363 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$a8840a6f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:07.380 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:07.388 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:07.447 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:07.453 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$a3bf3401] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:07.456 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:07.462 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$523914ca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:07.469 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:07.471 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:07.473 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-03-24 02:12:07.512 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-03-24 02:12:08.091 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-03-24 02:12:08.092 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-03-24 02:12:08.092 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-03-24 02:12:08.092 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-03-24 02:12:08.092 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:08.103 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:08.111 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:08.910 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:08.919 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:08.923 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:08.925 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:08.957 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:08.958 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy119] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:08.966 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:08.967 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy120] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:08.969 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.008 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.024 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.045 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.046 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.049 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.071 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.073 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy126] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.111 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.113 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy127] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.117 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.133 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.155 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$700427c1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.160 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$2250e9da] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.162 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$dd67cfc9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.169 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.173 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.180 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$43346f7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.185 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.189 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$520fc550] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.198 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.199 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.205 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.207 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.229 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.251 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.257 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.257 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.265 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.280 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:12:09.779 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2023-03-24 02:12:09.794 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2023-03-24 02:12:09.803 ziyun [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-24 02:12:09.804 ziyun [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2023-03-24 02:12:09.968 ziyun [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-24 02:12:09.968 ziyun [restartedMain] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 7529 ms
2023-03-24 02:12:10.689 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-03-24 02:12:10.700 ziyun [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-03-24 02:12:10.700 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-03-24 02:12:10.704 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-03-24 02:12:10.706 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-03-24 02:12:10.707 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'DESKTOP-EAM764B1679595130690'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-03-24 02:12:10.707 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-03-24 02:12:10.707 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-03-24 02:12:10.708 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@f5a49f
2023-03-24 02:12:10.761 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:10 | 耗时 15 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-03-24 02:12:11.689 ziyun [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-03-24 02:12:11.774 ziyun [restartedMain] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-03-24 02:12:11.814 ziyun [restartedMain] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-03-24 02:12:11.993 ziyun [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2023-03-24 02:12:12.702 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-03-24 02:12:12.719 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-03-24 02:12:12.761 ziyun [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-03-24 02:12:12.897 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-03-24 02:12:12.907 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-03-24 02:12:12.917 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-03-24 02:12:12.922 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-03-24 02:12:12.935 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-03-24 02:12:12.972 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-03-24 02:12:12.984 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-03-24 02:12:12.999 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-03-24 02:12:13.001 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-03-24 02:12:13.007 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-03-24 02:12:13.021 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-03-24 02:12:13.050 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2023-03-24 02:12:13.072 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2023-03-24 02:12:13.074 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 11.579 seconds (JVM running for 20.481)
2023-03-24 02:12:13.083 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-03-24 02:12:13.095 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-03-24 02:12:13.096 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-03-24 02:12:13.164 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 21 ms | SQL 语句：
SELECT USER_ID,username,password,DEPT_ID,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-03-24 02:12:13.192 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 18 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-03-24 02:12:13.255 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 17 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-03-24 02:12:13.277 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-03-24 02:12:13.299 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166' ;
2023-03-24 02:12:13.318 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-03-24 02:12:13.338 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 17 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-03-24 02:12:13.357 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-03-24 02:12:13.375 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167' ;
2023-03-24 02:12:13.394 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-03-24 02:12:13.413 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-03-24 02:12:13.432 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-03-24 02:12:13.449 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171' ;
2023-03-24 02:12:13.469 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 18 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-03-24 02:12:13.489 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-03-24 02:12:13.507 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-03-24 02:12:13.527 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 17 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172' ;
2023-03-24 02:12:13.546 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-03-24 02:12:13.568 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 17 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-03-24 02:12:13.587 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-03-24 02:12:13.608 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 17 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174' ;
2023-03-24 02:12:13.627 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-03-24 02:12:13.645 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-03-24 02:12:13.663 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-03-24 02:12:13.682 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 17 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175' ;
2023-03-24 02:12:13.701 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 18 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-03-24 02:12:13.719 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-03-24 02:12:13.737 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 16 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-03-24 02:12:13.755 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176' ;
2023-03-24 02:12:13.775 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-03-24 02:12:13.792 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 14 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-03-24 02:12:13.809 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-03-24 02:12:13.832 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177' ;
2023-03-24 02:12:13.849 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 15 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-03-24 02:12:13.866 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 15 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-03-24 02:12:13.884 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 16 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-03-24 02:12:13.902 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178' ;
2023-03-24 02:12:13.920 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-03-24 02:12:13.937 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 15 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-03-24 02:12:13.956 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 16 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-03-24 02:12:13.975 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 17 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179' ;
2023-03-24 02:12:13.997 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:13 | 耗时 18 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-03-24 02:12:14.018 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:14 | 耗时 15 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-03-24 02:12:14.040 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:14 | 耗时 16 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-03-24 02:12:14.065 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:14 | 耗时 18 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180' ;
2023-03-24 02:12:14.084 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:14 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-03-24 02:12:14.101 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:14 | 耗时 14 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-03-24 02:12:14.119 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:14 | 耗时 16 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-03-24 02:12:14.138 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:14 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181' ;
2023-03-24 02:12:14.155 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:14 | 耗时 15 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'phonedispose' group by u.username;
2023-03-24 02:12:14.173 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:14 | 耗时 15 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'phonedispose';
2023-03-24 02:12:14.189 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:14 | 耗时 14 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'phonedispose' and m.perms is not null and m.perms <> '';
2023-03-24 02:12:14.206 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:12:14 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='182' ;
2023-03-24 02:12:14.207 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-03-24 02:12:14.208 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-03-24 02:12:14.208 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-03-24 02:12:14.208 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-03-24 02:12:14.209 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-03-24T02:12:14.209
2023-03-24 02:14:43.465 ziyun [http-nio-9528-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-03-24 02:14:43.465 ziyun [http-nio-9528-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2023-03-24 02:14:43.516 ziyun [http-nio-9528-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 50 ms
2023-03-24 02:14:43.635 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-03-24 02:14:43 | 耗时 16 ms | SQL 语句：
SELECT t_user.REAL_NAME AS xAxis, COUNT(*) AS user_count FROM t_user LEFT JOIN t_repair_info ON t_user.USER_ID = t_repair_info.PROCESSOR_ID WHERE YEAR(t_repair_info.ADD_TIME) = YEAR(NOW()) GROUP BY t_user.USER_ID;;
2023-03-24 02:16:31.538 ziyun [http-nio-9528-exec-4] INFO  p6spy - 2023-03-24 02:16:31 | 耗时 20 ms | SQL 语句：
SELECT t_user.REAL_NAME AS xAxis, COUNT(*) AS user_count FROM t_user LEFT JOIN t_repair_info ON t_user.USER_ID = t_repair_info.PROCESSOR_ID WHERE YEAR(t_repair_info.ADD_TIME) = YEAR(NOW()) GROUP BY t_user.USER_ID;;
2023-03-24 02:16:34.681 ziyun [http-nio-9528-exec-6] INFO  p6spy - 2023-03-24 02:16:34 | 耗时 20 ms | SQL 语句：
SELECT t_user.REAL_NAME AS xAxis, COUNT(*) AS user_count FROM t_user LEFT JOIN t_repair_info ON t_user.USER_ID = t_repair_info.PROCESSOR_ID WHERE YEAR(t_repair_info.ADD_TIME) = YEAR(NOW()) GROUP BY t_user.USER_ID;;
2023-03-24 02:20:21.841 ziyun [http-nio-9528-exec-9] INFO  p6spy - 2023-03-24 02:20:21 | 耗时 18 ms | SQL 语句：
SELECT t_user.REAL_NAME AS xAxis, COUNT(*) AS user_count FROM t_user LEFT JOIN t_repair_info ON t_user.USER_ID = t_repair_info.PROCESSOR_ID WHERE YEAR(t_repair_info.ADD_TIME) = YEAR(NOW()) GROUP BY t_user.USER_ID;;
2023-03-24 02:20:47.267 ziyun [Thread-9] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2023-03-24 02:20:47.268 ziyun [Thread-9] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2023-03-24 02:20:47.268 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1679595130690 shutting down.
2023-03-24 02:20:47.268 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1679595130690 paused.
2023-03-24 02:20:47.269 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1679595130690 shutdown complete.
2023-03-24 02:20:47.271 ziyun [Thread-9] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2023-03-24 02:20:47.272 ziyun [Thread-9] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2023-03-24 02:20:47.276 ziyun [Thread-9] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
2023-03-24 02:20:57.420 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on DESKTOP-EAM764B with PID 63912 (started by karl in D:\project\Java Projectes\queuingsystem)
2023-03-24 02:20:57.422 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-03-24 02:20:57.463 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2023-03-24 02:20:57.463 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2023-03-24 02:20:58.910 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-03-24 02:20:58.911 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-03-24 02:20:59.000 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 69ms. Found 0 repository interfaces.
2023-03-24 02:20:59.451 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$54053b40] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:20:59.616 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:20:59.619 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$bd93ac11] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:20:59.627 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:20:59.631 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:20:59.631 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$a83f842b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:00.067 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$e8189d7f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:00.082 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:00.086 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:00.147 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:00.155 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$e353c711] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:00.158 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:00.164 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$91cda7da] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:00.172 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:00.173 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:00.176 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-03-24 02:21:00.213 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-03-24 02:21:00.812 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-03-24 02:21:00.813 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-03-24 02:21:00.813 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-03-24 02:21:00.813 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-03-24 02:21:00.813 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:00.831 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:00.841 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:01.992 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.003 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.007 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.010 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.047 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.055 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy119] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.065 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.066 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy120] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.068 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.116 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.134 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.156 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.158 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.161 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.180 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.182 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy126] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.236 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.245 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy127] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.250 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.268 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.287 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$af98bad1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.292 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$61e57cea] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.295 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$1cfc62d9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.303 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.307 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.324 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$43c7da07] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.330 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.335 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$91a45860] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.347 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.348 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.353 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.355 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.384 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.410 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.415 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.415 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.424 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.449 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:21:02.955 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2023-03-24 02:21:02.970 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2023-03-24 02:21:03.061 ziyun [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-24 02:21:03.062 ziyun [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2023-03-24 02:21:03.234 ziyun [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-24 02:21:03.234 ziyun [restartedMain] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5771 ms
2023-03-24 02:21:04.115 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-03-24 02:21:04.126 ziyun [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-03-24 02:21:04.127 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-03-24 02:21:04.130 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-03-24 02:21:04.132 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-03-24 02:21:04.133 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'DESKTOP-EAM764B1679595664116'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-03-24 02:21:04.133 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-03-24 02:21:04.133 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-03-24 02:21:04.134 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@5f913e
2023-03-24 02:21:04.210 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:04 | 耗时 14 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-03-24 02:21:05.528 ziyun [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-03-24 02:21:05.742 ziyun [restartedMain] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-03-24 02:21:05.810 ziyun [restartedMain] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-03-24 02:21:06.116 ziyun [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2023-03-24 02:21:07.141 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-03-24 02:21:07.162 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-03-24 02:21:07.224 ziyun [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-03-24 02:21:07.393 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-03-24 02:21:07.405 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-03-24 02:21:07.415 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-03-24 02:21:07.422 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-03-24 02:21:07.437 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-03-24 02:21:07.477 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-03-24 02:21:07.491 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-03-24 02:21:07.508 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-03-24 02:21:07.510 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-03-24 02:21:07.529 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-03-24 02:21:07.545 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-03-24 02:21:07.575 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2023-03-24 02:21:07.594 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2023-03-24 02:21:07.596 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 10.678 seconds (JVM running for 16.708)
2023-03-24 02:21:07.607 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-03-24 02:21:07.619 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-03-24 02:21:07.619 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-03-24 02:21:07.692 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:07 | 耗时 13 ms | SQL 语句：
SELECT USER_ID,username,password,DEPT_ID,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-03-24 02:21:07.712 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:07 | 耗时 14 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-03-24 02:21:07.765 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:07 | 耗时 13 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-03-24 02:21:07.785 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:07 | 耗时 14 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-03-24 02:21:07.806 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:07 | 耗时 12 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166' ;
2023-03-24 02:21:07.823 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:07 | 耗时 15 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-03-24 02:21:07.840 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:07 | 耗时 13 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-03-24 02:21:07.854 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:07 | 耗时 13 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-03-24 02:21:07.868 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:07 | 耗时 12 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167' ;
2023-03-24 02:21:07.885 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:07 | 耗时 15 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-03-24 02:21:07.901 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:07 | 耗时 12 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-03-24 02:21:07.917 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:07 | 耗时 14 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-03-24 02:21:07.931 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:07 | 耗时 13 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171' ;
2023-03-24 02:21:07.947 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:07 | 耗时 14 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-03-24 02:21:07.966 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:07 | 耗时 14 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-03-24 02:21:07.983 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:07 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-03-24 02:21:08.000 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172' ;
2023-03-24 02:21:08.017 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 15 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-03-24 02:21:08.034 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 12 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-03-24 02:21:08.049 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 13 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-03-24 02:21:08.062 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 11 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174' ;
2023-03-24 02:21:08.077 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 14 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-03-24 02:21:08.092 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 13 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-03-24 02:21:08.107 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 13 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-03-24 02:21:08.121 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 13 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175' ;
2023-03-24 02:21:08.139 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-03-24 02:21:08.159 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 14 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-03-24 02:21:08.176 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 13 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-03-24 02:21:08.207 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 12 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176' ;
2023-03-24 02:21:08.225 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-03-24 02:21:08.239 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 11 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-03-24 02:21:08.253 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 13 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-03-24 02:21:08.279 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 13 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177' ;
2023-03-24 02:21:08.294 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 14 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-03-24 02:21:08.308 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 11 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-03-24 02:21:08.321 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 11 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-03-24 02:21:08.334 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 11 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178' ;
2023-03-24 02:21:08.352 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-03-24 02:21:08.366 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 12 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-03-24 02:21:08.381 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 13 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-03-24 02:21:08.394 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 12 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179' ;
2023-03-24 02:21:08.410 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 14 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-03-24 02:21:08.425 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 12 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-03-24 02:21:08.444 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 13 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-03-24 02:21:08.463 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 12 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180' ;
2023-03-24 02:21:08.478 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 13 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-03-24 02:21:08.491 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 11 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-03-24 02:21:08.506 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 13 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-03-24 02:21:08.522 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 12 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181' ;
2023-03-24 02:21:08.537 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 13 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'phonedispose' group by u.username;
2023-03-24 02:21:08.551 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 11 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'phonedispose';
2023-03-24 02:21:08.566 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 13 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'phonedispose' and m.perms is not null and m.perms <> '';
2023-03-24 02:21:08.579 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:21:08 | 耗时 12 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='182' ;
2023-03-24 02:21:08.580 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-03-24 02:21:08.580 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-03-24 02:21:08.580 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-03-24 02:21:08.580 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-03-24 02:21:08.580 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-03-24T02:21:08.580
2023-03-24 02:21:16.631 ziyun [http-nio-9528-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-03-24 02:21:16.631 ziyun [http-nio-9528-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2023-03-24 02:21:16.656 ziyun [http-nio-9528-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 25 ms
2023-03-24 02:21:55.677 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-03-24 02:21:55 | 耗时 17 ms | SQL 语句：
SELECT t_user.REAL_NAME AS xAxis, COUNT(*) AS user_count FROM t_user LEFT JOIN t_repair_info ON t_user.USER_ID = t_repair_info.PROCESSOR_ID WHERE YEAR(t_repair_info.ADD_TIME) = YEAR(NOW()) GROUP BY t_user.USER_ID;;
2023-03-24 02:22:39.242 ziyun [Thread-9] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2023-03-24 02:22:39.243 ziyun [Thread-9] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2023-03-24 02:22:39.243 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1679595664116 shutting down.
2023-03-24 02:22:39.243 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1679595664116 paused.
2023-03-24 02:22:39.244 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1679595664116 shutdown complete.
2023-03-24 02:22:39.246 ziyun [Thread-9] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2023-03-24 02:22:39.246 ziyun [Thread-9] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2023-03-24 02:22:39.251 ziyun [Thread-9] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
2023-03-24 02:22:50.669 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on DESKTOP-EAM764B with PID 46880 (started by karl in D:\project\Java Projectes\queuingsystem)
2023-03-24 02:22:50.673 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-03-24 02:22:50.740 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2023-03-24 02:22:50.740 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2023-03-24 02:22:52.785 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-03-24 02:22:52.788 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-03-24 02:22:52.902 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 98ms. Found 0 repository interfaces.
2023-03-24 02:22:53.549 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$36431aa0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:53.789 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:53.794 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$9fd18b71] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:53.802 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:53.809 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:53.809 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$8a7d638b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:54.354 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$ca567cdf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:54.373 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:54.377 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:54.441 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:54.450 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$c591a671] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:54.453 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:54.461 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$740b873a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:54.480 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:54.482 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:54.485 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-03-24 02:22:54.519 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-03-24 02:22:55.060 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-03-24 02:22:55.060 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-03-24 02:22:55.060 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-03-24 02:22:55.060 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-03-24 02:22:55.060 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:55.072 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:55.080 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:55.949 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:55.956 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:55.960 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:55.963 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:55.994 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.001 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy119] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.009 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.010 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy120] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.012 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.048 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.079 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.103 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.104 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.109 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.129 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.130 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy126] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.176 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.184 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy127] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.190 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.206 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.225 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$91d69a31] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.229 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$44235c4a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.232 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$ff3a4239] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.241 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.245 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.251 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$2605b967] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.257 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.261 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$73e237c0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.278 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.280 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.291 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.294 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.317 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.337 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.342 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.342 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.350 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.379 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:22:56.923 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2023-03-24 02:22:56.936 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2023-03-24 02:22:56.945 ziyun [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-24 02:22:56.945 ziyun [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2023-03-24 02:22:57.114 ziyun [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-24 02:22:57.115 ziyun [restartedMain] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6375 ms
2023-03-24 02:22:58.049 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-03-24 02:22:58.066 ziyun [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-03-24 02:22:58.089 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-03-24 02:22:58.097 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-03-24 02:22:58.100 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-03-24 02:22:58.102 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'DESKTOP-EAM764B1679595778053'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-03-24 02:22:58.102 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-03-24 02:22:58.102 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-03-24 02:22:58.104 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@916785
2023-03-24 02:22:58.198 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:22:58 | 耗时 18 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-03-24 02:22:59.484 ziyun [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-03-24 02:22:59.633 ziyun [restartedMain] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-03-24 02:22:59.683 ziyun [restartedMain] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-03-24 02:22:59.935 ziyun [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2023-03-24 02:23:00.842 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-03-24 02:23:00.859 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-03-24 02:23:00.913 ziyun [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-03-24 02:23:01.064 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-03-24 02:23:01.075 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-03-24 02:23:01.085 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-03-24 02:23:01.091 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-03-24 02:23:01.105 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-03-24 02:23:01.161 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-03-24 02:23:01.173 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-03-24 02:23:01.182 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-03-24 02:23:01.184 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-03-24 02:23:01.192 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-03-24 02:23:01.206 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-03-24 02:23:01.235 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2023-03-24 02:23:01.253 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2023-03-24 02:23:01.256 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 11.653 seconds (JVM running for 17.506)
2023-03-24 02:23:01.265 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-03-24 02:23:01.276 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-03-24 02:23:01.277 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-03-24 02:23:01.358 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 20 ms | SQL 语句：
SELECT USER_ID,username,password,DEPT_ID,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-03-24 02:23:01.381 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 18 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-03-24 02:23:01.435 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-03-24 02:23:01.457 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 16 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-03-24 02:23:01.478 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166' ;
2023-03-24 02:23:01.501 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 20 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-03-24 02:23:01.532 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 17 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-03-24 02:23:01.549 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 16 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-03-24 02:23:01.566 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167' ;
2023-03-24 02:23:01.585 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-03-24 02:23:01.605 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-03-24 02:23:01.624 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-03-24 02:23:01.641 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171' ;
2023-03-24 02:23:01.660 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-03-24 02:23:01.681 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-03-24 02:23:01.705 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 22 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-03-24 02:23:01.723 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172' ;
2023-03-24 02:23:01.742 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-03-24 02:23:01.762 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 17 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-03-24 02:23:01.783 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 20 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-03-24 02:23:01.798 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 13 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174' ;
2023-03-24 02:23:01.815 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-03-24 02:23:01.832 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 14 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-03-24 02:23:01.849 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 16 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-03-24 02:23:01.864 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 13 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175' ;
2023-03-24 02:23:01.883 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-03-24 02:23:01.903 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 17 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-03-24 02:23:01.921 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 16 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-03-24 02:23:01.938 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176' ;
2023-03-24 02:23:01.962 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 22 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-03-24 02:23:01.981 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-03-24 02:23:01.999 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:01 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-03-24 02:23:02.016 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177' ;
2023-03-24 02:23:02.035 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-03-24 02:23:02.054 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-03-24 02:23:02.075 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 18 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-03-24 02:23:02.094 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178' ;
2023-03-24 02:23:02.115 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-03-24 02:23:02.134 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 15 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-03-24 02:23:02.151 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 16 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-03-24 02:23:02.168 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179' ;
2023-03-24 02:23:02.188 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 18 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-03-24 02:23:02.206 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 15 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-03-24 02:23:02.224 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 13 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-03-24 02:23:02.246 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180' ;
2023-03-24 02:23:02.264 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-03-24 02:23:02.282 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 15 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-03-24 02:23:02.302 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-03-24 02:23:02.323 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181' ;
2023-03-24 02:23:02.341 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'phonedispose' group by u.username;
2023-03-24 02:23:02.359 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'phonedispose';
2023-03-24 02:23:02.377 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'phonedispose' and m.perms is not null and m.perms <> '';
2023-03-24 02:23:02.397 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:23:02 | 耗时 18 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='182' ;
2023-03-24 02:23:02.397 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-03-24 02:23:02.397 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-03-24 02:23:02.397 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-03-24 02:23:02.397 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-03-24 02:23:02.397 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-03-24T02:23:02.397
2023-03-24 02:23:15.388 ziyun [http-nio-9528-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-03-24 02:23:15.388 ziyun [http-nio-9528-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2023-03-24 02:23:15.403 ziyun [http-nio-9528-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 15 ms
2023-03-24 02:23:26.604 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-03-24 02:23:26 | 耗时 21 ms | SQL 语句：
SELECT t_user.REAL_NAME AS xAxis, COUNT(*) AS count FROM t_user LEFT JOIN t_repair_info ON t_user.USER_ID = t_repair_info.PROCESSOR_ID WHERE YEAR(t_repair_info.ADD_TIME) = YEAR(NOW()) GROUP BY t_user.USER_ID;;
2023-03-24 02:28:43.366 ziyun [Thread-9] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2023-03-24 02:28:43.367 ziyun [Thread-9] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2023-03-24 02:28:43.367 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1679595778053 shutting down.
2023-03-24 02:28:43.367 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1679595778053 paused.
2023-03-24 02:28:43.367 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1679595778053 shutdown complete.
2023-03-24 02:28:43.370 ziyun [Thread-9] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2023-03-24 02:28:43.370 ziyun [Thread-9] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2023-03-24 02:28:43.374 ziyun [Thread-9] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
2023-03-24 02:28:53.481 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on DESKTOP-EAM764B with PID 64104 (started by karl in D:\project\Java Projectes\queuingsystem)
2023-03-24 02:28:53.485 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-03-24 02:28:53.541 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2023-03-24 02:28:53.541 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2023-03-24 02:28:55.776 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-03-24 02:28:55.779 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-03-24 02:28:55.936 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 135ms. Found 0 repository interfaces.
2023-03-24 02:28:56.567 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$353a9274] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:56.993 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:57.004 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$9ec90345] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:57.014 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:57.021 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:57.022 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$8974db5f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:57.641 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$c94df4b3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:57.660 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:57.664 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:57.736 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:57.744 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$c4891e45] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:57.748 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:57.755 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$7302ff0e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:57.764 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:57.766 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:57.768 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-03-24 02:28:57.803 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-03-24 02:28:58.368 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-03-24 02:28:58.368 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-03-24 02:28:58.368 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-03-24 02:28:58.368 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-03-24 02:28:58.368 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:58.380 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:58.388 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.105 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.113 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.117 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.120 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.155 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.156 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy119] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.165 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.170 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy120] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.173 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.207 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.229 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.260 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.262 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.265 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.284 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.285 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy126] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.324 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.326 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy127] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.331 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.347 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.368 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$90ce1205] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.372 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$431ad41e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.375 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$fe31ba0d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.382 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.386 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.391 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$24fd313b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.396 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.400 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$72d9af94] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.408 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.410 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.414 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.416 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.441 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.458 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.463 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.463 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.471 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.485 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:28:59.961 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2023-03-24 02:28:59.974 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2023-03-24 02:28:59.982 ziyun [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-24 02:28:59.982 ziyun [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2023-03-24 02:29:00.147 ziyun [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-24 02:29:00.147 ziyun [restartedMain] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6606 ms
2023-03-24 02:29:00.861 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-03-24 02:29:00.870 ziyun [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-03-24 02:29:00.871 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-03-24 02:29:00.874 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-03-24 02:29:00.875 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-03-24 02:29:00.876 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'DESKTOP-EAM764B1679596140862'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-03-24 02:29:00.876 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-03-24 02:29:00.876 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-03-24 02:29:00.877 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@2599ed
2023-03-24 02:29:00.931 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:00 | 耗时 17 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-03-24 02:29:01.861 ziyun [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-03-24 02:29:01.965 ziyun [restartedMain] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-03-24 02:29:02.008 ziyun [restartedMain] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-03-24 02:29:02.188 ziyun [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2023-03-24 02:29:02.945 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-03-24 02:29:02.963 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-03-24 02:29:03.010 ziyun [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-03-24 02:29:03.165 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-03-24 02:29:03.179 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-03-24 02:29:03.191 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-03-24 02:29:03.197 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-03-24 02:29:03.215 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-03-24 02:29:03.261 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-03-24 02:29:03.274 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-03-24 02:29:03.284 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-03-24 02:29:03.286 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-03-24 02:29:03.301 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-03-24 02:29:03.319 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-03-24 02:29:03.351 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2023-03-24 02:29:03.371 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2023-03-24 02:29:03.373 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 10.551 seconds (JVM running for 16.422)
2023-03-24 02:29:03.384 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-03-24 02:29:03.397 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-03-24 02:29:03.397 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-03-24 02:29:03.479 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 25 ms | SQL 语句：
SELECT USER_ID,username,password,DEPT_ID,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-03-24 02:29:03.512 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 19 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-03-24 02:29:03.575 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 18 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-03-24 02:29:03.600 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 18 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-03-24 02:29:03.632 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 25 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166' ;
2023-03-24 02:29:03.654 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 19 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-03-24 02:29:03.675 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 18 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-03-24 02:29:03.694 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 18 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-03-24 02:29:03.712 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167' ;
2023-03-24 02:29:03.731 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-03-24 02:29:03.752 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 17 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-03-24 02:29:03.771 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-03-24 02:29:03.790 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 17 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171' ;
2023-03-24 02:29:03.811 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 20 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-03-24 02:29:03.832 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-03-24 02:29:03.851 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-03-24 02:29:03.870 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 17 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172' ;
2023-03-24 02:29:03.888 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-03-24 02:29:03.908 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 17 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-03-24 02:29:03.927 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-03-24 02:29:03.946 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 17 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174' ;
2023-03-24 02:29:03.965 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-03-24 02:29:03.985 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:03 | 耗时 17 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-03-24 02:29:04.005 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 18 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-03-24 02:29:04.023 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 17 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175' ;
2023-03-24 02:29:04.042 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-03-24 02:29:04.062 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 17 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-03-24 02:29:04.081 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-03-24 02:29:04.100 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 17 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176' ;
2023-03-24 02:29:04.120 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 19 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-03-24 02:29:04.140 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 17 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-03-24 02:29:04.159 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-03-24 02:29:04.178 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 17 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177' ;
2023-03-24 02:29:04.207 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 21 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-03-24 02:29:04.226 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-03-24 02:29:04.244 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-03-24 02:29:04.264 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 17 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178' ;
2023-03-24 02:29:04.283 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-03-24 02:29:04.302 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 17 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-03-24 02:29:04.321 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-03-24 02:29:04.338 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179' ;
2023-03-24 02:29:04.356 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-03-24 02:29:04.375 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-03-24 02:29:04.396 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 16 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-03-24 02:29:04.418 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180' ;
2023-03-24 02:29:04.438 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-03-24 02:29:04.467 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 26 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-03-24 02:29:04.491 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 22 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-03-24 02:29:04.528 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 32 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181' ;
2023-03-24 02:29:04.548 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'phonedispose' group by u.username;
2023-03-24 02:29:04.569 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 18 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'phonedispose';
2023-03-24 02:29:04.590 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 20 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'phonedispose' and m.perms is not null and m.perms <> '';
2023-03-24 02:29:04.608 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:29:04 | 耗时 17 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='182' ;
2023-03-24 02:29:04.609 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-03-24 02:29:04.609 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-03-24 02:29:04.609 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-03-24 02:29:04.609 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-03-24 02:29:04.609 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-03-24T02:29:04.609
2023-03-24 02:29:08.985 ziyun [http-nio-9528-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-03-24 02:29:08.985 ziyun [http-nio-9528-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2023-03-24 02:29:09.000 ziyun [http-nio-9528-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 15 ms
2023-03-24 02:29:09.076 ziyun [http-nio-9528-exec-1] INFO  p6spy - 2023-03-24 02:29:09 | 耗时 20 ms | SQL 语句：
SELECT t_user.REAL_NAME AS xAxis, COUNT(*) AS count FROM t_user LEFT JOIN t_repair_info ON t_user.USER_ID = t_repair_info.PROCESSOR_ID WHERE YEAR(t_repair_info.ADD_TIME) = YEAR(NOW()) GROUP BY t_user.USER_ID;;
2023-03-24 02:31:07.329 ziyun [Thread-9] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2023-03-24 02:31:07.331 ziyun [Thread-9] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2023-03-24 02:31:07.331 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1679596140862 shutting down.
2023-03-24 02:31:07.331 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1679596140862 paused.
2023-03-24 02:31:07.331 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1679596140862 shutdown complete.
2023-03-24 02:31:07.335 ziyun [Thread-9] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2023-03-24 02:31:07.335 ziyun [Thread-9] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2023-03-24 02:31:07.339 ziyun [Thread-9] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
2023-03-24 02:31:18.136 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on DESKTOP-EAM764B with PID 21668 (started by karl in D:\project\Java Projectes\queuingsystem)
2023-03-24 02:31:18.139 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-03-24 02:31:18.185 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2023-03-24 02:31:18.185 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2023-03-24 02:31:19.844 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-03-24 02:31:19.846 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-03-24 02:31:19.952 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 90ms. Found 0 repository interfaces.
2023-03-24 02:31:20.509 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$3b60fff7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:20.660 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:20.664 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$a4ef70c8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:20.671 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:20.676 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:20.677 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$8f9b48e2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:21.223 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$cf746236] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:21.249 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:21.253 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:21.319 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:21.328 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$caaf8bc8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:21.331 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:21.339 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$79296c91] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:21.349 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:21.351 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:21.359 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-03-24 02:31:21.401 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-03-24 02:31:21.956 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-03-24 02:31:21.957 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-03-24 02:31:21.957 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-03-24 02:31:21.957 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-03-24 02:31:21.957 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:21.969 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:21.977 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.737 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.744 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.748 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.751 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.783 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.784 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy119] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.792 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.793 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy120] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.795 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.831 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.849 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.870 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.871 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.875 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.894 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.895 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy126] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.935 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.936 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy127] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.941 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.956 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.973 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$96f47f88] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.977 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$494141a1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.980 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$4582790] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.987 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:22.990 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:23.000 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$2b239ebe] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:23.005 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:23.009 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$79001d17] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:23.016 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:23.018 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:23.022 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:23.024 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:23.045 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:23.072 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:23.077 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:23.077 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:23.088 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:23.103 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-03-24 02:31:23.546 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2023-03-24 02:31:23.558 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2023-03-24 02:31:23.566 ziyun [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-24 02:31:23.566 ziyun [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2023-03-24 02:31:23.717 ziyun [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-24 02:31:23.717 ziyun [restartedMain] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5531 ms
2023-03-24 02:31:24.430 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-03-24 02:31:24.440 ziyun [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-03-24 02:31:24.440 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-03-24 02:31:24.443 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-03-24 02:31:24.445 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-03-24 02:31:24.445 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'DESKTOP-EAM764B1679596284431'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-03-24 02:31:24.445 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-03-24 02:31:24.445 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-03-24 02:31:24.446 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@1bc850
2023-03-24 02:31:24.502 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:24 | 耗时 13 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-03-24 02:31:25.461 ziyun [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-03-24 02:31:25.567 ziyun [restartedMain] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-03-24 02:31:25.605 ziyun [restartedMain] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-03-24 02:31:25.826 ziyun [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2023-03-24 02:31:26.621 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-03-24 02:31:26.643 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-03-24 02:31:26.685 ziyun [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-03-24 02:31:26.847 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-03-24 02:31:26.860 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-03-24 02:31:26.871 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-03-24 02:31:26.877 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-03-24 02:31:26.893 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-03-24 02:31:26.936 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-03-24 02:31:26.950 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-03-24 02:31:26.959 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-03-24 02:31:26.961 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-03-24 02:31:26.969 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-03-24 02:31:26.992 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-03-24 02:31:27.022 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2023-03-24 02:31:27.043 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2023-03-24 02:31:27.045 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 9.505 seconds (JVM running for 15.615)
2023-03-24 02:31:27.055 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-03-24 02:31:27.068 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-03-24 02:31:27.068 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-03-24 02:31:27.124 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,username,password,DEPT_ID,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-03-24 02:31:27.146 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-03-24 02:31:27.206 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-03-24 02:31:27.226 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-03-24 02:31:27.249 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 17 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166' ;
2023-03-24 02:31:27.264 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-03-24 02:31:27.280 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-03-24 02:31:27.305 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 24 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-03-24 02:31:27.321 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167' ;
2023-03-24 02:31:27.336 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-03-24 02:31:27.354 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-03-24 02:31:27.369 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 14 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-03-24 02:31:27.384 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171' ;
2023-03-24 02:31:27.400 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 14 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-03-24 02:31:27.417 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-03-24 02:31:27.433 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 14 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-03-24 02:31:27.449 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172' ;
2023-03-24 02:31:27.464 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 14 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-03-24 02:31:27.480 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-03-24 02:31:27.496 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 14 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-03-24 02:31:27.512 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174' ;
2023-03-24 02:31:27.531 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-03-24 02:31:27.547 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-03-24 02:31:27.562 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-03-24 02:31:27.578 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175' ;
2023-03-24 02:31:27.596 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-03-24 02:31:27.617 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 15 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-03-24 02:31:27.636 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-03-24 02:31:27.651 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176' ;
2023-03-24 02:31:27.666 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 14 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-03-24 02:31:27.681 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-03-24 02:31:27.698 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-03-24 02:31:27.713 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177' ;
2023-03-24 02:31:27.728 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 14 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-03-24 02:31:27.744 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-03-24 02:31:27.759 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-03-24 02:31:27.775 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178' ;
2023-03-24 02:31:27.790 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 14 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-03-24 02:31:27.805 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-03-24 02:31:27.820 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-03-24 02:31:27.835 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179' ;
2023-03-24 02:31:27.852 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 15 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-03-24 02:31:27.875 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 19 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-03-24 02:31:27.897 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 16 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-03-24 02:31:27.920 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180' ;
2023-03-24 02:31:27.936 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 14 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-03-24 02:31:27.951 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 13 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-03-24 02:31:27.968 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-03-24 02:31:27.988 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:27 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181' ;
2023-03-24 02:31:28.005 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:28 | 耗时 15 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'phonedispose' group by u.username;
2023-03-24 02:31:28.023 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:28 | 耗时 15 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'phonedispose';
2023-03-24 02:31:28.043 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:28 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'phonedispose' and m.perms is not null and m.perms <> '';
2023-03-24 02:31:28.063 ziyun [restartedMain] INFO  p6spy - 2023-03-24 02:31:28 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='182' ;
2023-03-24 02:31:28.064 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-03-24 02:31:28.064 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-03-24 02:31:28.064 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-03-24 02:31:28.064 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-03-24 02:31:28.065 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-03-24T02:31:28.065
2023-03-24 02:31:32.548 ziyun [http-nio-9528-exec-3] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-03-24 02:31:32.548 ziyun [http-nio-9528-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2023-03-24 02:31:32.559 ziyun [http-nio-9528-exec-3] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 11 ms
2023-03-24 02:31:32.625 ziyun [http-nio-9528-exec-3] INFO  p6spy - 2023-03-24 02:31:32 | 耗时 17 ms | SQL 语句：
SELECT t_user.REAL_NAME AS xAxis, COUNT(*) AS count FROM t_user LEFT JOIN t_repair_info ON t_user.USER_ID = t_repair_info.PROCESSOR_ID WHERE YEAR(t_repair_info.ADD_TIME) = YEAR(NOW()) GROUP BY t_user.USER_ID;;
2023-03-24 02:32:13.449 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-03-24 02:32:13 | 耗时 14 ms | SQL 语句：
SELECT d2.district_id,d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid WHERE d1.district_id = 1330000;
2023-03-24 02:32:14.038 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-03-24 02:32:14 | 耗时 586 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330100) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-03-24 02:32:14.724 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-03-24 02:32:14 | 耗时 683 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330300) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-03-24 02:32:15.377 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-03-24 02:32:15 | 耗时 651 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330400) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-03-24 02:32:15.751 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-03-24 02:32:15 | 耗时 373 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330500) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-03-24 02:32:16.223 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-03-24 02:32:16 | 耗时 469 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330600) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-03-24 02:32:16.615 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-03-24 02:32:16 | 耗时 386 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330700) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-03-24 02:32:16.869 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-03-24 02:32:16 | 耗时 249 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330800) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-03-24 02:32:17.078 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-03-24 02:32:17 | 耗时 207 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330900) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-03-24 02:32:17.680 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-03-24 02:32:17 | 耗时 597 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331000) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-03-24 02:32:17.991 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-03-24 02:32:17 | 耗时 306 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1331100) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-03-24 02:32:18.021 ziyun [http-nio-9528-exec-2] INFO  p6spy - 2023-03-24 02:32:18 | 耗时 28 ms | SQL 语句：
select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district from t_repair_info r JOIN t_swjg_info s ON r.SWJGDM = s.BSDTDJXH JOIN (SELECT d2.district_id, d2.district FROM t_district d1 JOIN t_district d2 ON d1.district_id = d2.pid /** 传入市的id */ WHERE d1.district_id = 1330091) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair RIGHT JOIN (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG group by tmp_status.STATUS_TAG order by tmp_status.STATUS_TAG;;
2023-03-24 02:32:34.778 ziyun [Thread-9] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2023-03-24 02:32:34.779 ziyun [Thread-9] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2023-03-24 02:32:34.779 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1679596284431 shutting down.
2023-03-24 02:32:34.779 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1679596284431 paused.
2023-03-24 02:32:34.779 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1679596284431 shutdown complete.
2023-03-24 02:32:34.786 ziyun [Thread-9] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2023-03-24 02:32:34.786 ziyun [Thread-9] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2023-03-24 02:32:34.790 ziyun [Thread-9] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
