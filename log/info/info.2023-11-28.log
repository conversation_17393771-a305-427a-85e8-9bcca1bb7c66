2023-11-28 16:55:44.868 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on MSI with PID 2764 (started by KarlK<PERSON> in D:\project\Java Projectes\queuingsystem)
2023-11-28 16:55:44.870 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-11-28 16:55:44.901 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2023-11-28 16:55:44.901 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2023-11-28 16:55:46.385 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-11-28 16:55:46.388 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-11-28 16:55:46.460 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 54ms. Found 0 repository interfaces.
2023-11-28 16:55:47.005 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$26b23346] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:47.157 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:47.160 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$9040a417] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:47.168 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:47.174 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:47.175 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$7aec7c31] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:47.547 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$bac59585] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:47.569 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:47.574 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:47.650 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:47.658 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$b600bf17] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:47.662 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:47.709 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$647a9fe0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:47.722 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:47.724 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:47.727 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-11-28 16:55:47.765 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-11-28 16:55:48.244 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-11-28 16:55:48.244 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-11-28 16:55:48.244 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-11-28 16:55:48.244 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-11-28 16:55:48.244 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:48.256 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:48.264 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.424 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.439 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.442 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.446 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.491 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.497 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy119] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.505 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.507 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy120] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.509 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.550 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.567 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.599 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.600 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.604 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.624 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.627 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy126] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.685 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.692 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy127] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.698 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.726 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.749 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$8245b2d7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.755 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$349274f0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.759 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$efa95adf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.767 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.773 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.780 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$1674d20d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.787 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.792 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$64515066] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.806 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.809 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.814 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.818 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.849 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.875 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.881 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.881 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.889 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:49.915 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:55:50.695 ziyun [restartedMain] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2023-11-28 16:55:50.695 ziyun [restartedMain] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5794 ms
2023-11-28 16:55:51.881 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-11-28 16:55:51.893 ziyun [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-11-28 16:55:51.895 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-11-28 16:55:51.899 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-11-28 16:55:51.902 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-11-28 16:55:51.902 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'MSI1701161751882'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-11-28 16:55:51.903 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-11-28 16:55:51.903 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-11-28 16:55:51.905 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@c18720
2023-11-28 16:55:51.975 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:51 | 耗时 9 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-11-28 16:55:53.358 ziyun [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-11-28 16:55:53.546 ziyun [restartedMain] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-11-28 16:55:53.610 ziyun [restartedMain] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-11-28 16:55:53.955 ziyun [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2023-11-28 16:55:55.249 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-11-28 16:55:55.275 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-11-28 16:55:55.363 ziyun [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-11-28 16:55:55.603 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-11-28 16:55:55.621 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-11-28 16:55:55.633 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-11-28 16:55:55.643 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-11-28 16:55:55.661 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-11-28 16:55:55.716 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-11-28 16:55:55.733 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-11-28 16:55:55.752 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-11-28 16:55:55.756 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-11-28 16:55:55.777 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-11-28 16:55:55.800 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-11-28 16:55:55.876 ziyun [restartedMain] INFO  org.xnio - XNIO version 3.3.8.Final
2023-11-28 16:55:55.894 ziyun [restartedMain] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.3.8.Final
2023-11-28 16:55:55.971 ziyun [restartedMain] INFO  o.s.b.w.e.u.UndertowServletWebServer - Undertow started on port(s) 9528 (http) with context path ''
2023-11-28 16:55:55.976 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 11.601 seconds (JVM running for 18.241)
2023-11-28 16:55:55.984 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-11-28 16:55:56.002 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-11-28 16:55:56.002 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-11-28 16:55:56.037 ziyun [XNIO-1 task-1] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-11-28 16:55:56.037 ziyun [XNIO-1 task-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2023-11-28 16:55:56.055 ziyun [XNIO-1 task-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 18 ms
2023-11-28 16:55:56.071 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
SELECT USER_ID,USERNAME,password,DEPT_ID,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-11-28 16:55:56.086 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 11 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-11-28 16:55:56.164 ziyun [XNIO-1 task-3] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 11 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-11-28 16:55:56.262 ziyun [XNIO-1 task-5] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 20 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-11-28 16:55:56.309 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-11-28 16:55:56.328 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-11-28 16:55:56.342 ziyun [XNIO-1 task-2] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 12 ms | SQL 语句：
SELECT USER_ID,USERNAME,password,DEPT_ID,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE DEPT_ID = '2';
2023-11-28 16:55:56.343 ziyun [XNIO-1 task-6] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 13 ms | SQL 语句：
SELECT USER_ID,USERNAME,password,DEPT_ID,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE DEPT_ID = '2';
2023-11-28 16:55:56.346 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166';
2023-11-28 16:55:56.358 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 11 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-11-28 16:55:56.372 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-11-28 16:55:56.383 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-11-28 16:55:56.398 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167';
2023-11-28 16:55:56.417 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 10 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-11-28 16:55:56.432 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-11-28 16:55:56.438 ziyun [XNIO-1 task-4] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 11 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '2000-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-11-28 16:55:56.440 ziyun [XNIO-1 task-1] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 14 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '2000-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-11-28 16:55:56.442 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-11-28 16:55:56.452 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171';
2023-11-28 16:55:56.465 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 10 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-11-28 16:55:56.468 ziyun [XNIO-1 task-1] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 24 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID,DEAL_TYPE FROM t_repair_info WHERE ADD_TIME >= '2000-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-11-28 16:55:56.481 ziyun [XNIO-1 task-9] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 10 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district;
2023-11-28 16:55:56.488 ziyun [XNIO-1 task-8] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,USERNAME,password,DEPT_ID,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user WHERE DEPT_ID = '2';
2023-11-28 16:55:56.492 ziyun [XNIO-1 task-7] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 15 ms | SQL 语句：
SELECT COUNT(1) FROM t_repair_info WHERE ADD_TIME >= '2000-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00';
2023-11-28 16:55:56.493 ziyun [XNIO-1 task-4] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 49 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID,DEAL_TYPE FROM t_repair_info WHERE ADD_TIME >= '2000-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-11-28 16:55:56.499 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-11-28 16:55:56.506 ziyun [XNIO-1 task-7] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 14 ms | SQL 语句：
SELECT REPAIR_ID,NAME,MOBILE,TITLE,ADDRESS,STATE_TAG,REPAIR_NUMBER,ADD_TIME,REVIEW_TIME,DEAL_TIME,PARTIAL_COMPLETION_TIME,COMPLETION_TIME,CONT,IMG,REVIEWS,HANDLE_CONT,RECEIPT,SWJGDM,BSDTMC,USER_ID,REAL_NAME,PROCESSOR_ID,DEAL_TYPE FROM t_repair_info WHERE ADD_TIME >= '2000-01-01 00:00:00' AND ADD_TIME <= '2900-01-01 00:00:00' ORDER BY REPAIR_ID DESC LIMIT 0,15;
2023-11-28 16:55:56.509 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 8 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-11-28 16:55:56.520 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172';
2023-11-28 16:55:56.532 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 10 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-11-28 16:55:56.544 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-11-28 16:55:56.554 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-11-28 16:55:56.564 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 7 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174';
2023-11-28 16:55:56.576 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 10 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-11-28 16:55:56.589 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-11-28 16:55:56.600 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-11-28 16:55:56.612 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175';
2023-11-28 16:55:56.623 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 10 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-11-28 16:55:56.635 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-11-28 16:55:56.647 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-11-28 16:55:56.657 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176';
2023-11-28 16:55:56.667 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-11-28 16:55:56.679 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-11-28 16:55:56.689 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 8 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-11-28 16:55:56.699 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 7 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177';
2023-11-28 16:55:56.710 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-11-28 16:55:56.721 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-11-28 16:55:56.732 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-11-28 16:55:56.741 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 7 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178';
2023-11-28 16:55:56.751 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-11-28 16:55:56.763 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-11-28 16:55:56.775 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 8 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-11-28 16:55:56.784 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179';
2023-11-28 16:55:56.795 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-11-28 16:55:56.806 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-11-28 16:55:56.818 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-11-28 16:55:56.832 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180';
2023-11-28 16:55:56.845 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-11-28 16:55:56.855 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 7 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-11-28 16:55:56.867 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-11-28 16:55:56.880 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181';
2023-11-28 16:55:56.891 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'phonedispose' group by u.username;
2023-11-28 16:55:56.901 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'phonedispose';
2023-11-28 16:55:56.911 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'phonedispose' and m.perms is not null and m.perms <> '';
2023-11-28 16:55:56.922 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:55:56 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='182';
2023-11-28 16:55:56.922 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-11-28 16:55:56.923 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-11-28 16:55:56.923 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-11-28 16:55:56.923 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-11-28 16:55:56.923 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-11-28T16:55:56.923
2023-11-28 16:56:26.813 ziyun [XNIO-1 task-10] INFO  c.z.r.c.c.DingTalkController - 传过来的:80.40.98.215
80.40.98.204
80.40.98.203
三个摄像头黑屏
2023-11-28 16:56:38.378 ziyun [XNIO-1 task-10] INFO  p6spy - 2023-11-28 16:56:38 | 耗时 8 ms | SQL 语句：
SELECT t2.district_id,t2.pid,t2.district,t2.level FROM t_swjg_info t1 JOIN t_district t2 ON t1.district_id = t2.district_id WHERE t1.BSDTDJXH = '1330000240';
2023-11-28 16:56:38.387 ziyun [XNIO-1 task-10] INFO  p6spy - 2023-11-28 16:56:38 | 耗时 7 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district WHERE district_id='1331000';
2023-11-28 16:56:38.415 ziyun [Thread-8] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2023-11-28 16:56:38.417 ziyun [Thread-8] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2023-11-28 16:56:38.417 ziyun [Thread-8] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1701161751882 shutting down.
2023-11-28 16:56:38.417 ziyun [Thread-8] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1701161751882 paused.
2023-11-28 16:56:38.418 ziyun [Thread-8] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1701161751882 shutdown complete.
2023-11-28 16:56:38.421 ziyun [Thread-8] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2023-11-28 16:56:38.421 ziyun [Thread-8] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2023-11-28 16:56:38.431 ziyun [Thread-8] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
2023-11-28 16:56:38.435 ziyun [Thread-8] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2023-11-28 16:56:39.823 ziyun [XNIO-1 task-10] INFO  c.z.r.c.c.DingTalkController - Inserted data: [2023-11-28 16:56:04, 台州-温岭市, 国家税务总局温岭市税务局第一税务所-第一税务所办税服务厅, 温岭市太平街道, 2023-11-28 11:36:07, 监控半球, 80.40.98.215
80.40.98.204
80.40.98.203
三个摄像头黑屏]
2023-11-28 16:57:14.947 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on MSI with PID 25300 (started by KarlKyo in D:\project\Java Projectes\queuingsystem)
2023-11-28 16:57:14.949 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-11-28 16:57:14.985 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2023-11-28 16:57:14.985 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2023-11-28 16:57:16.266 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-11-28 16:57:16.268 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-11-28 16:57:16.326 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 40ms. Found 0 repository interfaces.
2023-11-28 16:57:16.759 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$9c50d658] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:16.912 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:16.915 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$5df4729] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:16.921 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:16.926 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:16.927 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$f08b1f43] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:17.200 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$30643897] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:17.215 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:17.217 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:17.274 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:17.281 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$2b9f6229] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:17.286 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:17.297 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$da1942f2] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:17.308 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:17.310 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:17.312 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-11-28 16:57:17.340 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-11-28 16:57:17.749 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-11-28 16:57:17.749 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-11-28 16:57:17.749 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-11-28 16:57:17.749 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-11-28 16:57:17.749 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:17.759 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:17.766 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.651 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.662 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.667 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.670 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.705 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.711 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy119] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.718 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.720 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy120] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.721 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.767 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.780 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.799 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.800 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.803 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.829 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.830 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy126] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.871 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.879 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy127] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.885 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.901 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.923 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$f7e455e9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.927 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$aa311802] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.929 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$6547fdf1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.935 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.939 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.944 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$8c13751f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.951 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.956 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$d9eff378] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.964 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.967 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.970 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.972 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:18.996 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:19.015 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:19.019 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:19.019 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:19.026 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:19.038 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-11-28 16:57:19.603 ziyun [restartedMain] INFO  io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2023-11-28 16:57:19.603 ziyun [restartedMain] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 4618 ms
2023-11-28 16:57:20.470 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-11-28 16:57:20.484 ziyun [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-11-28 16:57:20.484 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-11-28 16:57:20.488 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-11-28 16:57:20.490 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-11-28 16:57:20.491 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'MSI1701161840476'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-11-28 16:57:20.491 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-11-28 16:57:20.491 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-11-28 16:57:20.492 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@795e1c
2023-11-28 16:57:20.538 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:20 | 耗时 9 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-11-28 16:57:21.543 ziyun [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-11-28 16:57:21.655 ziyun [restartedMain] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-11-28 16:57:21.695 ziyun [restartedMain] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-11-28 16:57:21.920 ziyun [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2023-11-28 16:57:22.730 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-11-28 16:57:22.759 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-11-28 16:57:22.798 ziyun [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-11-28 16:57:23.021 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-11-28 16:57:23.032 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-11-28 16:57:23.040 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-11-28 16:57:23.045 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-11-28 16:57:23.056 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-11-28 16:57:23.091 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-11-28 16:57:23.101 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-11-28 16:57:23.110 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-11-28 16:57:23.111 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-11-28 16:57:23.124 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-11-28 16:57:23.138 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-11-28 16:57:23.185 ziyun [restartedMain] INFO  org.xnio - XNIO version 3.3.8.Final
2023-11-28 16:57:23.193 ziyun [restartedMain] INFO  org.xnio.nio - XNIO NIO Implementation Version 3.3.8.Final
2023-11-28 16:57:23.238 ziyun [restartedMain] INFO  o.s.b.w.e.u.UndertowServletWebServer - Undertow started on port(s) 9528 (http) with context path ''
2023-11-28 16:57:23.240 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 8.764 seconds (JVM running for 14.42)
2023-11-28 16:57:23.243 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-11-28 16:57:23.254 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-11-28 16:57:23.254 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-11-28 16:57:23.299 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 9 ms | SQL 语句：
SELECT USER_ID,USERNAME,password,DEPT_ID,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-11-28 16:57:23.313 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 10 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-11-28 16:57:23.373 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-11-28 16:57:23.388 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-11-28 16:57:23.401 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166';
2023-11-28 16:57:23.411 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 9 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-11-28 16:57:23.422 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-11-28 16:57:23.431 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-11-28 16:57:23.441 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167';
2023-11-28 16:57:23.451 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 9 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-11-28 16:57:23.463 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-11-28 16:57:23.473 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-11-28 16:57:23.481 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171';
2023-11-28 16:57:23.491 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 9 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-11-28 16:57:23.503 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-11-28 16:57:23.514 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-11-28 16:57:23.523 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172';
2023-11-28 16:57:23.536 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 11 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-11-28 16:57:23.547 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-11-28 16:57:23.557 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-11-28 16:57:23.566 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174';
2023-11-28 16:57:23.577 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 9 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-11-28 16:57:23.588 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-11-28 16:57:23.598 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-11-28 16:57:23.607 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175';
2023-11-28 16:57:23.617 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 9 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-11-28 16:57:23.627 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-11-28 16:57:23.636 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-11-28 16:57:23.647 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176';
2023-11-28 16:57:23.656 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-11-28 16:57:23.666 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-11-28 16:57:23.674 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 7 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-11-28 16:57:23.684 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 7 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177';
2023-11-28 16:57:23.694 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-11-28 16:57:23.704 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-11-28 16:57:23.714 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-11-28 16:57:23.723 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 7 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178';
2023-11-28 16:57:23.734 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-11-28 16:57:23.745 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 7 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-11-28 16:57:23.753 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-11-28 16:57:23.762 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 7 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179';
2023-11-28 16:57:23.771 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-11-28 16:57:23.782 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 7 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-11-28 16:57:23.796 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-11-28 16:57:23.809 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 7 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180';
2023-11-28 16:57:23.820 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-11-28 16:57:23.829 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 7 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-11-28 16:57:23.841 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-11-28 16:57:23.852 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 7 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181';
2023-11-28 16:57:23.862 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 8 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'phonedispose' group by u.username;
2023-11-28 16:57:23.871 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 7 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'phonedispose';
2023-11-28 16:57:23.880 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 7 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'phonedispose' and m.perms is not null and m.perms <> '';
2023-11-28 16:57:23.889 ziyun [restartedMain] INFO  p6spy - 2023-11-28 16:57:23 | 耗时 7 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='182';
2023-11-28 16:57:23.889 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-11-28 16:57:23.889 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-11-28 16:57:23.889 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-11-28 16:57:23.889 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-11-28 16:57:23.889 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-11-28T16:57:23.889
2023-11-28 16:57:25.113 ziyun [XNIO-1 task-1] INFO  io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-11-28 16:57:25.114 ziyun [XNIO-1 task-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2023-11-28 16:57:25.128 ziyun [XNIO-1 task-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 14 ms
2023-11-28 16:57:36.069 ziyun [XNIO-1 task-1] INFO  c.z.r.c.c.DingTalkController - 传过来的:80.40.98.215
80.40.98.204
80.40.98.203
三个摄像头黑屏
2023-11-28 16:57:44.373 ziyun [XNIO-1 task-1] INFO  p6spy - 2023-11-28 16:57:44 | 耗时 8 ms | SQL 语句：
SELECT t2.district_id,t2.pid,t2.district,t2.level FROM t_swjg_info t1 JOIN t_district t2 ON t1.district_id = t2.district_id WHERE t1.BSDTDJXH = '1330000240';
2023-11-28 16:57:44.381 ziyun [XNIO-1 task-1] INFO  p6spy - 2023-11-28 16:57:44 | 耗时 6 ms | SQL 语句：
SELECT district_id,pid,district,level FROM t_district WHERE district_id='1331000';
2023-11-28 16:57:44.393 ziyun [Thread-8] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2023-11-28 16:57:44.396 ziyun [Thread-8] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2023-11-28 16:57:44.396 ziyun [Thread-8] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1701161840476 shutting down.
2023-11-28 16:57:44.396 ziyun [Thread-8] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1701161840476 paused.
2023-11-28 16:57:44.397 ziyun [Thread-8] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1701161840476 shutdown complete.
2023-11-28 16:57:44.399 ziyun [Thread-8] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2023-11-28 16:57:44.400 ziyun [Thread-8] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2023-11-28 16:57:44.404 ziyun [Thread-8] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
2023-11-28 16:57:44.408 ziyun [Thread-8] INFO  io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2023-11-28 16:57:45.559 ziyun [XNIO-1 task-1] INFO  c.z.r.c.c.DingTalkController - Inserted data: [2023-11-28 16:57:25, 台州-温岭市, 国家税务总局温岭市税务局第一税务所-第一税务所办税服务厅, 温岭市太平街道, 2023-11-28 11:36:07, 监控半球, 80.40.98.215,80.40.98.204,80.40.98.203,三个摄像头黑屏]
