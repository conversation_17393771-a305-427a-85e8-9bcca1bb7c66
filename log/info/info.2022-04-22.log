2022-04-22 16:23:09.076 ziyun [main] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on DESKTOP-EAM764B with PID 21580 (started by karl in D:\project\Java Projectes\queuingsystem)
2022-04-22 16:23:09.081 ziyun [main] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2022-04-22 16:23:10.928 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2022-04-22 16:23:10.931 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2022-04-22 16:23:11.055 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 96ms. Found 0 repository interfaces.
2022-04-22 16:23:11.708 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$575d7378] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:11.889 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:11.893 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$c0ebe449] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:11.902 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:11.908 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:11.909 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$ab97bc63] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:12.460 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$eb70d5b7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:12.482 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:12.486 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:12.578 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:12.588 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$e6abff49] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:12.592 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:12.603 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$9525e012] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:12.617 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:12.618 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:12.622 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2022-04-22 16:23:12.679 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2022-04-22 16:23:13.260 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2022-04-22 16:23:13.260 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2022-04-22 16:23:13.260 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2022-04-22 16:23:13.261 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2022-04-22 16:23:13.261 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:13.276 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:13.285 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.058 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.066 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.074 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.077 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy114] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.118 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.119 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy116] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.128 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.129 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.131 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.174 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.193 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.216 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.218 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy121] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.222 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.244 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.250 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.295 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.296 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.301 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.318 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.338 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$b2f0f309] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.343 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$653db522] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.348 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$20549b11] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.355 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.358 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.369 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$4720123f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.376 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.381 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$94fc9098] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.392 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.394 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.398 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.401 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.427 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.446 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.453 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.453 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.461 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.476 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:14.911 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2022-04-22 16:23:14.924 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2022-04-22 16:23:14.935 ziyun [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2022-04-22 16:23:14.936 ziyun [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2022-04-22 16:23:15.118 ziyun [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2022-04-22 16:23:15.118 ziyun [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5986 ms
2022-04-22 16:23:15.920 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2022-04-22 16:23:15.931 ziyun [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2022-04-22 16:23:15.931 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2022-04-22 16:23:15.934 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2022-04-22 16:23:15.936 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2022-04-22 16:23:15.938 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'DESKTOP-EAM764B1650615795922'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2022-04-22 16:23:15.938 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2022-04-22 16:23:15.938 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2022-04-22 16:23:15.940 ziyun [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@b9c575
2022-04-22 16:23:16.000 ziyun [main] INFO  p6spy - 2022-04-22 16:23:15 | 耗时 10 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2022-04-22 16:23:16.990 ziyun [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2022-04-22 16:23:17.092 ziyun [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2022-04-22 16:23:17.141 ziyun [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2022-04-22 16:23:18.088 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2022-04-22 16:23:18.106 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2022-04-22 16:23:18.146 ziyun [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2022-04-22 16:23:18.298 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2022-04-22 16:23:18.320 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2022-04-22 16:23:18.332 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2022-04-22 16:23:18.338 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2022-04-22 16:23:18.356 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2022-04-22 16:23:18.396 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2022-04-22 16:23:18.401 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2022-04-22 16:23:18.411 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2022-04-22 16:23:18.423 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2022-04-22 16:23:18.426 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2022-04-22 16:23:18.434 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2022-04-22 16:23:18.452 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_10
2022-04-22 16:23:18.489 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2022-04-22 16:23:18.512 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2022-04-22 16:23:18.514 ziyun [main] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 10.136 seconds (JVM running for 16.149)
2022-04-22 16:23:18.526 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2022-04-22 16:23:18.542 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2022-04-22 16:23:18.542 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2022-04-22 16:23:18.589 ziyun [main] INFO  p6spy - 2022-04-22 16:23:18 | 耗时 10 ms | SQL 语句：
SELECT USER_ID,username,password,dept_id,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2022-04-22 16:23:18.617 ziyun [main] INFO  p6spy - 2022-04-22 16:23:18 | 耗时 22 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2022-04-22 16:23:18.677 ziyun [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2022-04-22 16:23:18.680 ziyun [main] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2022-04-22 16:23:18.680 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1650615795922 shutting down.
2022-04-22 16:23:18.680 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1650615795922 paused.
2022-04-22 16:23:18.681 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1650615795922 shutdown complete.
2022-04-22 16:23:18.684 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2022-04-22 16:23:18.684 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2022-04-22 16:23:18.689 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
2022-04-22 16:23:55.769 ziyun [main] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on DESKTOP-EAM764B with PID 16344 (started by karl in D:\project\Java Projectes\queuingsystem)
2022-04-22 16:23:55.773 ziyun [main] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2022-04-22 16:23:57.460 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2022-04-22 16:23:57.463 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2022-04-22 16:23:57.588 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 110ms. Found 0 repository interfaces.
2022-04-22 16:23:58.315 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$d20e2b4e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:58.495 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:58.500 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$3b9c9c1f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:58.515 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:58.521 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:58.522 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$26487439] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:59.086 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$66218d8d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:59.111 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:59.116 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:59.213 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:59.226 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$615cb71f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:59.230 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:59.240 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$fd697e8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:59.252 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:59.255 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:59.258 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2022-04-22 16:23:59.311 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2022-04-22 16:23:59.935 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2022-04-22 16:23:59.935 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2022-04-22 16:23:59.935 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2022-04-22 16:23:59.936 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2022-04-22 16:23:59.936 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:59.952 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:23:59.960 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.756 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.763 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.766 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.769 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy114] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.812 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.813 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy116] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.822 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.824 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.826 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.861 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.878 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.908 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.910 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy121] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.913 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.936 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.936 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.975 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.976 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:00.982 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.005 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.028 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$2da1aadf] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.033 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$dfee6cf8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.036 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$9b0552e7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.046 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.050 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.055 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$c1d0ca15] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.060 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.063 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$fad486e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.072 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.074 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.078 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.081 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.111 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.126 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.132 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.133 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.140 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.154 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:24:01.573 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2022-04-22 16:24:01.585 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2022-04-22 16:24:01.596 ziyun [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2022-04-22 16:24:01.596 ziyun [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2022-04-22 16:24:01.779 ziyun [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2022-04-22 16:24:01.780 ziyun [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5955 ms
2022-04-22 16:24:02.585 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2022-04-22 16:24:02.598 ziyun [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2022-04-22 16:24:02.598 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2022-04-22 16:24:02.600 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2022-04-22 16:24:02.602 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2022-04-22 16:24:02.603 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'DESKTOP-EAM764B1650615842586'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2022-04-22 16:24:02.603 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2022-04-22 16:24:02.603 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2022-04-22 16:24:02.604 ziyun [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@1c90a05
2022-04-22 16:24:02.660 ziyun [main] INFO  p6spy - 2022-04-22 16:24:02 | 耗时 12 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2022-04-22 16:24:03.640 ziyun [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2022-04-22 16:24:03.761 ziyun [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2022-04-22 16:24:03.801 ziyun [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2022-04-22 16:24:04.744 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2022-04-22 16:24:04.762 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2022-04-22 16:24:04.811 ziyun [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2022-04-22 16:24:04.963 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2022-04-22 16:24:04.975 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2022-04-22 16:24:04.986 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2022-04-22 16:24:04.993 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2022-04-22 16:24:05.010 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2022-04-22 16:24:05.047 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2022-04-22 16:24:05.052 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2022-04-22 16:24:05.063 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2022-04-22 16:24:05.079 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2022-04-22 16:24:05.082 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2022-04-22 16:24:05.089 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2022-04-22 16:24:05.106 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_10
2022-04-22 16:24:05.137 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2022-04-22 16:24:05.157 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2022-04-22 16:24:05.159 ziyun [main] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 10.126 seconds (JVM running for 16.038)
2022-04-22 16:24:05.169 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2022-04-22 16:24:05.193 ziyun [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2022-04-22 16:24:05.195 ziyun [main] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2022-04-22 16:24:05.195 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1650615842586 shutting down.
2022-04-22 16:24:05.195 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1650615842586 paused.
2022-04-22 16:24:05.196 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1650615842586 shutdown complete.
2022-04-22 16:24:05.198 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2022-04-22 16:24:05.199 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2022-04-22 16:24:05.203 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
2022-04-22 16:30:28.389 ziyun [main] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on DESKTOP-EAM764B with PID 19868 (started by karl in D:\project\Java Projectes\queuingsystem)
2022-04-22 16:30:28.392 ziyun [main] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2022-04-22 16:30:30.072 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2022-04-22 16:30:30.074 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2022-04-22 16:30:30.191 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 100ms. Found 0 repository interfaces.
2022-04-22 16:30:30.775 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$499c88ef] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:31.022 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:31.026 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$b32af9c0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:31.034 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:31.041 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:31.041 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$9dd6d1da] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:31.625 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$ddafeb2e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:31.651 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:31.657 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:31.762 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:31.774 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$d8eb14c0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:31.781 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:31.794 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$8764f589] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:31.808 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:31.811 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:31.814 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2022-04-22 16:30:31.887 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2022-04-22 16:30:32.429 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2022-04-22 16:30:32.429 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2022-04-22 16:30:32.429 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2022-04-22 16:30:32.429 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2022-04-22 16:30:32.430 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:32.452 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:32.462 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.220 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.228 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.231 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.234 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy114] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.271 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.273 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy116] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.282 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.283 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.285 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.325 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.344 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.368 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.371 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy121] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.375 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.401 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.402 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.441 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.442 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.447 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.464 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.486 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$a5300880] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.491 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$577cca99] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.493 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$1293b088] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.508 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.511 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.517 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$395f27b6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.522 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.524 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$873ba60f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.534 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.536 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.540 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.543 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.569 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.590 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.598 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.598 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.608 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:33.621 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:30:34.067 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2022-04-22 16:30:34.084 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2022-04-22 16:30:34.094 ziyun [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2022-04-22 16:30:34.094 ziyun [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2022-04-22 16:30:34.268 ziyun [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2022-04-22 16:30:34.268 ziyun [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 5824 ms
2022-04-22 16:30:35.080 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2022-04-22 16:30:35.098 ziyun [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2022-04-22 16:30:35.098 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2022-04-22 16:30:35.102 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2022-04-22 16:30:35.103 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2022-04-22 16:30:35.104 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'DESKTOP-EAM764B1650616235081'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2022-04-22 16:30:35.104 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2022-04-22 16:30:35.104 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2022-04-22 16:30:35.105 ziyun [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@a22e19
2022-04-22 16:30:35.158 ziyun [main] INFO  p6spy - 2022-04-22 16:30:35 | 耗时 12 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2022-04-22 16:30:36.159 ziyun [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2022-04-22 16:30:36.273 ziyun [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2022-04-22 16:30:36.311 ziyun [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2022-04-22 16:30:37.343 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2022-04-22 16:30:37.380 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2022-04-22 16:30:37.446 ziyun [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2022-04-22 16:30:37.617 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2022-04-22 16:30:37.634 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2022-04-22 16:30:37.648 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2022-04-22 16:30:37.656 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2022-04-22 16:30:37.682 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2022-04-22 16:30:37.735 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2022-04-22 16:30:37.741 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2022-04-22 16:30:37.754 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2022-04-22 16:30:37.778 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2022-04-22 16:30:37.782 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2022-04-22 16:30:37.791 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2022-04-22 16:30:37.811 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_10
2022-04-22 16:30:37.855 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2022-04-22 16:30:37.885 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2022-04-22 16:30:37.888 ziyun [main] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 10.323 seconds (JVM running for 16.273)
2022-04-22 16:30:37.904 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2022-04-22 16:30:37.955 ziyun [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2022-04-22 16:30:37.958 ziyun [main] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2022-04-22 16:30:37.958 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1650616235081 shutting down.
2022-04-22 16:30:37.959 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1650616235081 paused.
2022-04-22 16:30:37.959 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1650616235081 shutdown complete.
2022-04-22 16:30:37.963 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2022-04-22 16:30:37.964 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2022-04-22 16:30:37.970 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
2022-04-22 16:37:29.731 ziyun [main] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on DESKTOP-EAM764B with PID 20352 (started by karl in D:\project\Java Projectes\queuingsystem)
2022-04-22 16:37:29.735 ziyun [main] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2022-04-22 16:37:31.631 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2022-04-22 16:37:31.634 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2022-04-22 16:37:31.750 ziyun [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 98ms. Found 0 repository interfaces.
2022-04-22 16:37:32.408 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$89411620] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:32.593 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:32.597 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$f2cf86f1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:32.605 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:32.611 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:32.612 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$dd7b5f0b] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:33.173 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$1d54785f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:33.194 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:33.198 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:33.279 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:33.288 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$188fa1f1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:33.291 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:33.300 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$c70982ba] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:33.313 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:33.315 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:33.318 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2022-04-22 16:37:33.366 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2022-04-22 16:37:33.898 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2022-04-22 16:37:33.898 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2022-04-22 16:37:33.899 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2022-04-22 16:37:33.899 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2022-04-22 16:37:33.899 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:33.915 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:33.924 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:34.791 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:34.801 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:34.806 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:34.809 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy114] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:34.849 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:34.850 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy116] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:34.861 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:34.862 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:34.865 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:34.920 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:34.942 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:34.968 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:34.971 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy121] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:34.981 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.011 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.014 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.067 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.069 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.078 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.104 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.139 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$e4d495b1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.146 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$972157ca] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.148 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$52383db9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.156 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.162 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.169 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$7903b4e7] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.175 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.178 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$c6e03340] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.189 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.191 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.197 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.200 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.233 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.251 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.259 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.259 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.269 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.285 ziyun [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2022-04-22 16:37:35.711 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2022-04-22 16:37:35.726 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2022-04-22 16:37:35.736 ziyun [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2022-04-22 16:37:35.736 ziyun [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2022-04-22 16:37:35.895 ziyun [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2022-04-22 16:37:35.896 ziyun [main] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 6101 ms
2022-04-22 16:37:36.758 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2022-04-22 16:37:36.778 ziyun [main] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2022-04-22 16:37:36.778 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2022-04-22 16:37:36.782 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2022-04-22 16:37:36.785 ziyun [main] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2022-04-22 16:37:36.786 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'DESKTOP-EAM764B1650616656759'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2022-04-22 16:37:36.786 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2022-04-22 16:37:36.786 ziyun [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2022-04-22 16:37:36.789 ziyun [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@1fa6889
2022-04-22 16:37:36.858 ziyun [main] INFO  p6spy - 2022-04-22 16:37:36 | 耗时 14 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2022-04-22 16:37:37.888 ziyun [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2022-04-22 16:37:38.006 ziyun [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2022-04-22 16:37:38.047 ziyun [main] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2022-04-22 16:37:38.950 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2022-04-22 16:37:38.973 ziyun [main] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2022-04-22 16:37:39.014 ziyun [main] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2022-04-22 16:37:39.150 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2022-04-22 16:37:39.162 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2022-04-22 16:37:39.172 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2022-04-22 16:37:39.178 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2022-04-22 16:37:39.196 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2022-04-22 16:37:39.242 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2022-04-22 16:37:39.246 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2022-04-22 16:37:39.258 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2022-04-22 16:37:39.270 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2022-04-22 16:37:39.272 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2022-04-22 16:37:39.281 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2022-04-22 16:37:39.297 ziyun [main] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_10
2022-04-22 16:37:39.328 ziyun [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2022-04-22 16:37:39.347 ziyun [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2022-04-22 16:37:39.348 ziyun [main] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 10.326 seconds (JVM running for 16.479)
2022-04-22 16:37:39.366 ziyun [main] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2022-04-22 16:37:39.389 ziyun [main] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2022-04-22 16:37:39.391 ziyun [main] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2022-04-22 16:37:39.392 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1650616656759 shutting down.
2022-04-22 16:37:39.392 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1650616656759 paused.
2022-04-22 16:37:39.392 ziyun [main] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_DESKTOP-EAM764B1650616656759 shutdown complete.
2022-04-22 16:37:39.395 ziyun [main] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2022-04-22 16:37:39.395 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2022-04-22 16:37:39.401 ziyun [main] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
