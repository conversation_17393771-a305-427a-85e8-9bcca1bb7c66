2023-09-08 02:10:09.727 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on MSI with PID 24828 (started by KarlK<PERSON> in D:\project\Java Projectes\queuingsystem)
2023-09-08 02:10:09.728 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-09-08 02:10:09.752 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2023-09-08 02:10:09.752 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2023-09-08 02:10:10.906 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-09-08 02:10:10.907 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-09-08 02:10:10.942 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 24ms. Found 0 repository interfaces.
2023-09-08 02:10:11.205 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$66942ac3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:11.303 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:11.309 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$d0229b94] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:11.312 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:11.314 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:11.316 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$bace73ae] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:11.482 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$faa78d02] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:11.491 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:11.494 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:11.530 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:11.534 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$f5e2b694] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:11.536 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:11.539 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$a45c975d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:11.544 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:11.544 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:11.547 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-09-08 02:10:11.564 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-09-08 02:10:11.902 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-09-08 02:10:11.902 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-09-08 02:10:11.902 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-09-08 02:10:11.902 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-09-08 02:10:11.902 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:11.912 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:11.918 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.437 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.441 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.442 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.444 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.466 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.470 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy119] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.475 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.476 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy120] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.476 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.496 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.504 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.521 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.522 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.524 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.534 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.535 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy126] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.559 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.562 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy127] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.565 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.578 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.587 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$c227aa54] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.590 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$74746c6d] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.591 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$2f8b525c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.595 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.597 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.600 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$5656c98a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.603 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.604 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$a43347e3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.609 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.610 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.612 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.613 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.630 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.637 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.641 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.641 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.645 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.651 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:12.921 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2023-09-08 02:10:12.928 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2023-09-08 02:10:12.932 ziyun [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-09-08 02:10:12.932 ziyun [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2023-09-08 02:10:13.003 ziyun [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-09-08 02:10:13.003 ziyun [restartedMain] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 3251 ms
2023-09-08 02:10:13.493 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-09-08 02:10:13.506 ziyun [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-09-08 02:10:13.506 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-09-08 02:10:13.509 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-09-08 02:10:13.510 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-09-08 02:10:13.510 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'MSI1694110213493'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-09-08 02:10:13.510 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-09-08 02:10:13.510 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-09-08 02:10:13.511 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@1f57f31
2023-09-08 02:10:13.545 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:13 | 耗时 11 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-09-08 02:10:14.194 ziyun [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-09-08 02:10:14.261 ziyun [restartedMain] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-09-08 02:10:14.291 ziyun [restartedMain] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-09-08 02:10:14.445 ziyun [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2023-09-08 02:10:14.932 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-09-08 02:10:14.944 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-09-08 02:10:14.967 ziyun [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-09-08 02:10:15.060 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-09-08 02:10:15.066 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-09-08 02:10:15.071 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-09-08 02:10:15.074 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-09-08 02:10:15.086 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-09-08 02:10:15.106 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-09-08 02:10:15.113 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-09-08 02:10:15.118 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-09-08 02:10:15.118 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-09-08 02:10:15.122 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-09-08 02:10:15.134 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-09-08 02:10:15.156 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2023-09-08 02:10:15.169 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2023-09-08 02:10:15.171 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 5.72 seconds (JVM running for 11.945)
2023-09-08 02:10:15.177 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-09-08 02:10:15.191 ziyun [restartedMain] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2023-09-08 02:10:15.192 ziyun [restartedMain] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2023-09-08 02:10:15.192 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1694110213493 shutting down.
2023-09-08 02:10:15.192 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1694110213493 paused.
2023-09-08 02:10:15.192 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1694110213493 shutdown complete.
2023-09-08 02:10:15.194 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2023-09-08 02:10:15.194 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2023-09-08 02:10:15.196 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
2023-09-08 02:10:41.761 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on MSI with PID 30444 (started by KarlKyo in D:\project\Java Projectes\queuingsystem)
2023-09-08 02:10:41.763 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-09-08 02:10:41.789 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2023-09-08 02:10:41.789 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2023-09-08 02:10:42.540 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-09-08 02:10:42.542 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-09-08 02:10:42.575 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 26ms. Found 0 repository interfaces.
2023-09-08 02:10:42.849 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$60acbc43] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:42.946 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:42.948 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$ca3b2d14] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:42.956 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:42.959 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:42.959 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$b4e7052e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:43.100 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$f4c01e82] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:43.111 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:43.113 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:43.151 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:43.156 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$effb4814] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:43.158 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:43.162 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$9e7528dd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:43.166 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:43.167 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:43.169 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-09-08 02:10:43.188 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-09-08 02:10:43.572 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-09-08 02:10:43.572 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-09-08 02:10:43.572 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-09-08 02:10:43.572 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-09-08 02:10:43.572 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:43.579 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:43.589 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.051 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.056 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.059 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.060 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.077 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.080 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy119] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.088 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.092 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy120] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.093 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.112 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.120 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.134 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.139 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.141 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.151 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.152 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy126] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.174 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.178 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy127] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.180 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.190 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.206 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$bc403bd4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.208 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$6e8cfded] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.209 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$29a3e3dc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.212 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.214 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.217 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$506f5b0a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.221 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.223 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$9e4bd963] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.228 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.228 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.230 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.231 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.246 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.256 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.259 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.259 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.262 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.269 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:10:44.535 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2023-09-08 02:10:44.542 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2023-09-08 02:10:44.546 ziyun [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-09-08 02:10:44.546 ziyun [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2023-09-08 02:10:44.615 ziyun [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-09-08 02:10:44.615 ziyun [restartedMain] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2826 ms
2023-09-08 02:10:45.101 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-09-08 02:10:45.107 ziyun [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-09-08 02:10:45.107 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-09-08 02:10:45.109 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-09-08 02:10:45.110 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-09-08 02:10:45.110 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'MSI1694110245101'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-09-08 02:10:45.110 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-09-08 02:10:45.110 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-09-08 02:10:45.111 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@4edf64
2023-09-08 02:10:45.158 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:45 | 耗时 16 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-09-08 02:10:45.805 ziyun [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-09-08 02:10:45.870 ziyun [restartedMain] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-09-08 02:10:45.905 ziyun [restartedMain] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-09-08 02:10:46.044 ziyun [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2023-09-08 02:10:46.538 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-09-08 02:10:46.549 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-09-08 02:10:46.572 ziyun [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-09-08 02:10:46.670 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-09-08 02:10:46.675 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-09-08 02:10:46.680 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-09-08 02:10:46.683 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-09-08 02:10:46.694 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-09-08 02:10:46.716 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-09-08 02:10:46.722 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-09-08 02:10:46.727 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-09-08 02:10:46.728 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-09-08 02:10:46.733 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-09-08 02:10:46.746 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-09-08 02:10:46.768 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2023-09-08 02:10:46.781 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2023-09-08 02:10:46.783 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 5.324 seconds (JVM running for 10.657)
2023-09-08 02:10:46.790 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-09-08 02:10:46.797 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-09-08 02:10:46.797 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-09-08 02:10:46.858 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:46 | 耗时 30 ms | SQL 语句：
SELECT USER_ID,username,password,DEPT_ID,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-09-08 02:10:46.877 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:46 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-09-08 02:10:46.961 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:46 | 耗时 15 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-09-08 02:10:46.981 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:46 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-09-08 02:10:46.999 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:46 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166' ;
2023-09-08 02:10:47.015 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 14 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-09-08 02:10:47.033 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-09-08 02:10:47.049 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-09-08 02:10:47.064 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167' ;
2023-09-08 02:10:47.081 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-09-08 02:10:47.098 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-09-08 02:10:47.114 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-09-08 02:10:47.130 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171' ;
2023-09-08 02:10:47.147 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-09-08 02:10:47.165 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-09-08 02:10:47.181 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-09-08 02:10:47.197 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172' ;
2023-09-08 02:10:47.213 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-09-08 02:10:47.230 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-09-08 02:10:47.247 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-09-08 02:10:47.262 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174' ;
2023-09-08 02:10:47.279 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-09-08 02:10:47.298 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 14 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-09-08 02:10:47.314 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-09-08 02:10:47.330 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175' ;
2023-09-08 02:10:47.347 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-09-08 02:10:47.363 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 14 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-09-08 02:10:47.379 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-09-08 02:10:47.395 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176' ;
2023-09-08 02:10:47.411 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 14 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-09-08 02:10:47.426 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 14 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-09-08 02:10:47.443 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-09-08 02:10:47.458 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177' ;
2023-09-08 02:10:47.475 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-09-08 02:10:47.491 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 14 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-09-08 02:10:47.507 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-09-08 02:10:47.522 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178' ;
2023-09-08 02:10:47.539 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-09-08 02:10:47.557 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-09-08 02:10:47.573 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-09-08 02:10:47.588 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179' ;
2023-09-08 02:10:47.605 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-09-08 02:10:47.623 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-09-08 02:10:47.641 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-09-08 02:10:47.660 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180' ;
2023-09-08 02:10:47.681 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 18 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-09-08 02:10:47.699 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-09-08 02:10:47.716 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-09-08 02:10:47.738 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181' ;
2023-09-08 02:10:47.755 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'phonedispose' group by u.username;
2023-09-08 02:10:47.771 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 14 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'phonedispose';
2023-09-08 02:10:47.787 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'phonedispose' and m.perms is not null and m.perms <> '';
2023-09-08 02:10:47.802 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:10:47 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='182' ;
2023-09-08 02:10:47.803 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-09-08 02:10:47.803 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-09-08 02:10:47.803 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-09-08 02:10:47.803 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-09-08 02:10:47.803 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-09-08T02:10:47.803
2023-09-08 02:10:59.442 ziyun [Thread-9] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2023-09-08 02:10:59.442 ziyun [Thread-9] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2023-09-08 02:10:59.442 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1694110245101 shutting down.
2023-09-08 02:10:59.442 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1694110245101 paused.
2023-09-08 02:10:59.442 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1694110245101 shutdown complete.
2023-09-08 02:10:59.444 ziyun [Thread-9] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2023-09-08 02:10:59.444 ziyun [Thread-9] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2023-09-08 02:10:59.447 ziyun [Thread-9] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
2023-09-08 02:11:06.039 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on MSI with PID 17568 (started by KarlKyo in D:\project\Java Projectes\queuingsystem)
2023-09-08 02:11:06.043 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-09-08 02:11:06.065 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2023-09-08 02:11:06.065 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2023-09-08 02:11:06.804 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-09-08 02:11:06.808 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-09-08 02:11:06.838 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22ms. Found 0 repository interfaces.
2023-09-08 02:11:07.132 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$cc0530f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:07.206 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:07.208 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$764ec3e0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:07.216 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:07.219 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:07.219 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$60fa9bfa] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:07.358 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$a0d3b54e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:07.368 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:07.370 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:07.409 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:07.415 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$9c0edee0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:07.416 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:07.419 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$4a88bfa9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:07.423 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:07.425 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:07.427 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-09-08 02:11:07.445 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-09-08 02:11:07.852 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-09-08 02:11:07.852 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-09-08 02:11:07.852 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-09-08 02:11:07.852 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-09-08 02:11:07.852 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:07.861 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:07.865 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.347 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.351 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.353 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.355 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.374 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.374 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy119] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.378 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.380 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy120] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.381 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.407 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.415 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.426 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.427 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.432 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.444 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.445 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy126] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.467 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.471 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy127] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.474 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.482 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.497 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$6853d2a0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.499 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$1aa094b9] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.501 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$d5b77aa8] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.505 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.508 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.511 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$fc82f1d6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.514 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.515 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$4a5f702f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.521 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.522 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.524 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.525 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.541 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.548 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.550 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.551 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.554 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.564 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:11:08.821 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2023-09-08 02:11:08.829 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2023-09-08 02:11:08.833 ziyun [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-09-08 02:11:08.833 ziyun [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2023-09-08 02:11:08.904 ziyun [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-09-08 02:11:08.904 ziyun [restartedMain] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2839 ms
2023-09-08 02:11:09.398 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-09-08 02:11:09.406 ziyun [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-09-08 02:11:09.406 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-09-08 02:11:09.408 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-09-08 02:11:09.409 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-09-08 02:11:09.409 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'MSI1694110269399'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-09-08 02:11:09.409 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-09-08 02:11:09.409 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-09-08 02:11:09.410 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@14bba21
2023-09-08 02:11:09.449 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:09 | 耗时 16 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-09-08 02:11:10.125 ziyun [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-09-08 02:11:10.197 ziyun [restartedMain] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-09-08 02:11:10.223 ziyun [restartedMain] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-09-08 02:11:10.357 ziyun [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2023-09-08 02:11:10.850 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-09-08 02:11:10.861 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-09-08 02:11:10.887 ziyun [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-09-08 02:11:10.983 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-09-08 02:11:10.993 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-09-08 02:11:10.997 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-09-08 02:11:11.001 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-09-08 02:11:11.012 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-09-08 02:11:11.034 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-09-08 02:11:11.040 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-09-08 02:11:11.044 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-09-08 02:11:11.047 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-09-08 02:11:11.050 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-09-08 02:11:11.058 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-09-08 02:11:11.087 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2023-09-08 02:11:11.100 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2023-09-08 02:11:11.102 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 5.341 seconds (JVM running for 10.678)
2023-09-08 02:11:11.109 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-09-08 02:11:11.116 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-09-08 02:11:11.116 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-09-08 02:11:11.168 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 18 ms | SQL 语句：
SELECT USER_ID,username,password,DEPT_ID,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-09-08 02:11:11.191 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 19 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-09-08 02:11:11.241 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 18 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-09-08 02:11:11.263 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 18 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-09-08 02:11:11.282 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166' ;
2023-09-08 02:11:11.301 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-09-08 02:11:11.319 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-09-08 02:11:11.338 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-09-08 02:11:11.355 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167' ;
2023-09-08 02:11:11.373 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-09-08 02:11:11.393 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-09-08 02:11:11.409 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-09-08 02:11:11.426 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171' ;
2023-09-08 02:11:11.444 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-09-08 02:11:11.463 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 17 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-09-08 02:11:11.481 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-09-08 02:11:11.498 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172' ;
2023-09-08 02:11:11.531 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 32 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-09-08 02:11:11.549 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 15 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-09-08 02:11:11.567 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-09-08 02:11:11.584 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 15 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174' ;
2023-09-08 02:11:11.601 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-09-08 02:11:11.619 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-09-08 02:11:11.637 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-09-08 02:11:11.653 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175' ;
2023-09-08 02:11:11.671 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-09-08 02:11:11.688 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-09-08 02:11:11.707 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-09-08 02:11:11.725 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176' ;
2023-09-08 02:11:11.741 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-09-08 02:11:11.759 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-09-08 02:11:11.776 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 17 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-09-08 02:11:11.794 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177' ;
2023-09-08 02:11:11.814 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 18 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-09-08 02:11:11.833 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 17 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-09-08 02:11:11.851 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 18 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-09-08 02:11:11.868 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178' ;
2023-09-08 02:11:11.886 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-09-08 02:11:11.903 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-09-08 02:11:11.922 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-09-08 02:11:11.939 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179' ;
2023-09-08 02:11:11.956 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 17 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-09-08 02:11:11.975 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-09-08 02:11:11.996 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:11 | 耗时 18 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-09-08 02:11:12.024 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:12 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180' ;
2023-09-08 02:11:12.042 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:12 | 耗时 16 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-09-08 02:11:12.060 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:12 | 耗时 15 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-09-08 02:11:12.077 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:12 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-09-08 02:11:12.093 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:12 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181' ;
2023-09-08 02:11:12.110 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:12 | 耗时 15 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'phonedispose' group by u.username;
2023-09-08 02:11:12.127 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:12 | 耗时 16 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'phonedispose';
2023-09-08 02:11:12.144 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:12 | 耗时 15 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'phonedispose' and m.perms is not null and m.perms <> '';
2023-09-08 02:11:12.159 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:11:12 | 耗时 14 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='182' ;
2023-09-08 02:11:12.160 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-09-08 02:11:12.160 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-09-08 02:11:12.160 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-09-08 02:11:12.160 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-09-08 02:11:12.160 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-09-08T02:11:12.160
2023-09-08 02:11:54.107 ziyun [http-nio-9528-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-09-08 02:11:54.107 ziyun [http-nio-9528-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2023-09-08 02:11:54.117 ziyun [http-nio-9528-exec-2] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 10 ms
2023-09-08 02:12:34.572 ziyun [Thread-9] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2023-09-08 02:12:34.572 ziyun [Thread-9] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2023-09-08 02:12:34.572 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1694110269399 shutting down.
2023-09-08 02:12:34.572 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1694110269399 paused.
2023-09-08 02:12:34.572 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1694110269399 shutdown complete.
2023-09-08 02:12:34.573 ziyun [Thread-9] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2023-09-08 02:12:34.574 ziyun [Thread-9] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2023-09-08 02:12:34.576 ziyun [Thread-9] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
2023-09-08 02:12:42.332 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on MSI with PID 16484 (started by KarlKyo in D:\project\Java Projectes\queuingsystem)
2023-09-08 02:12:42.333 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-09-08 02:12:42.350 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2023-09-08 02:12:42.350 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2023-09-08 02:12:42.974 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-09-08 02:12:42.975 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-09-08 02:12:43.005 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 23ms. Found 0 repository interfaces.
2023-09-08 02:12:43.249 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$2b714c23] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:43.309 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:43.310 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$94ffbcf4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:43.316 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:43.318 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:43.318 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$7fab950e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:43.451 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$bf84ae62] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:43.460 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:43.462 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:43.493 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:43.497 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$babfd7f4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:43.499 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:43.502 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$6939b8bd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:43.509 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:43.510 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:43.511 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-09-08 02:12:43.527 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-09-08 02:12:43.876 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-09-08 02:12:43.876 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-09-08 02:12:43.876 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-09-08 02:12:43.876 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-09-08 02:12:43.876 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:43.882 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:43.886 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.263 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.266 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.268 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.269 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.288 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.289 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy119] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.293 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.294 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy120] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.294 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.313 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.321 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.334 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.335 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.337 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.347 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.348 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy126] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.368 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.369 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy127] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.374 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.382 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.391 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$8704cbb4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.394 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$39518dcd] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.395 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$f46873bc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.398 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.400 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.403 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$1b33eaea] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.405 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.406 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$69106943] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.411 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.414 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.415 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.416 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.429 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.436 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.439 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.439 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.442 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.451 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-09-08 02:12:44.673 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2023-09-08 02:12:44.679 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2023-09-08 02:12:44.683 ziyun [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-09-08 02:12:44.683 ziyun [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2023-09-08 02:12:44.745 ziyun [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-09-08 02:12:44.745 ziyun [restartedMain] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 2395 ms
2023-09-08 02:12:45.146 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-09-08 02:12:45.151 ziyun [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-09-08 02:12:45.151 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-09-08 02:12:45.153 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-09-08 02:12:45.154 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-09-08 02:12:45.154 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'MSI1694110365146'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-09-08 02:12:45.155 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-09-08 02:12:45.155 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-09-08 02:12:45.155 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@1690e46
2023-09-08 02:12:45.184 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:45 | 耗时 7 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-09-08 02:12:45.732 ziyun [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-09-08 02:12:45.784 ziyun [restartedMain] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-09-08 02:12:45.809 ziyun [restartedMain] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-09-08 02:12:45.913 ziyun [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2023-09-08 02:12:46.290 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-09-08 02:12:46.299 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-09-08 02:12:46.322 ziyun [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-09-08 02:12:46.397 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-09-08 02:12:46.402 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-09-08 02:12:46.408 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-09-08 02:12:46.411 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-09-08 02:12:46.417 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-09-08 02:12:46.440 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-09-08 02:12:46.446 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-09-08 02:12:46.450 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-09-08 02:12:46.451 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-09-08 02:12:46.455 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-09-08 02:12:46.463 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-09-08 02:12:46.477 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2023-09-08 02:12:46.488 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2023-09-08 02:12:46.489 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 4.377 seconds (JVM running for 9.724)
2023-09-08 02:12:46.495 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-09-08 02:12:46.501 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-09-08 02:12:46.502 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-09-08 02:12:46.539 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 10 ms | SQL 语句：
SELECT USER_ID,username,password,DEPT_ID,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-09-08 02:12:46.550 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-09-08 02:12:46.579 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-09-08 02:12:46.591 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-09-08 02:12:46.603 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166' ;
2023-09-08 02:12:46.613 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 9 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-09-08 02:12:46.623 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-09-08 02:12:46.630 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 7 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-09-08 02:12:46.639 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 7 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167' ;
2023-09-08 02:12:46.649 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 9 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-09-08 02:12:46.662 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 7 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-09-08 02:12:46.673 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-09-08 02:12:46.680 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 7 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171' ;
2023-09-08 02:12:46.689 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-09-08 02:12:46.699 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-09-08 02:12:46.707 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 7 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-09-08 02:12:46.716 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172' ;
2023-09-08 02:12:46.725 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-09-08 02:12:46.735 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 7 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-09-08 02:12:46.745 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-09-08 02:12:46.754 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174' ;
2023-09-08 02:12:46.763 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-09-08 02:12:46.773 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-09-08 02:12:46.783 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-09-08 02:12:46.792 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175' ;
2023-09-08 02:12:46.801 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-09-08 02:12:46.811 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-09-08 02:12:46.819 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 7 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-09-08 02:12:46.827 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 7 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176' ;
2023-09-08 02:12:46.835 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-09-08 02:12:46.844 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 7 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-09-08 02:12:46.852 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 7 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-09-08 02:12:46.862 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177' ;
2023-09-08 02:12:46.871 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-09-08 02:12:46.880 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-09-08 02:12:46.889 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 7 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-09-08 02:12:46.897 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 7 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178' ;
2023-09-08 02:12:46.906 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-09-08 02:12:46.917 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 9 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-09-08 02:12:46.927 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-09-08 02:12:46.937 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 9 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179' ;
2023-09-08 02:12:46.949 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 11 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-09-08 02:12:46.959 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-09-08 02:12:46.972 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 9 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-09-08 02:12:46.985 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 9 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180' ;
2023-09-08 02:12:46.995 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:46 | 耗时 9 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-09-08 02:12:47.004 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:47 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-09-08 02:12:47.015 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:47 | 耗时 10 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-09-08 02:12:47.026 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:47 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181' ;
2023-09-08 02:12:47.036 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:47 | 耗时 9 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'phonedispose' group by u.username;
2023-09-08 02:12:47.047 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:47 | 耗时 8 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'phonedispose';
2023-09-08 02:12:47.054 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:47 | 耗时 7 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'phonedispose' and m.perms is not null and m.perms <> '';
2023-09-08 02:12:47.064 ziyun [restartedMain] INFO  p6spy - 2023-09-08 02:12:47 | 耗时 8 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='182' ;
2023-09-08 02:12:47.064 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-09-08 02:12:47.064 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-09-08 02:12:47.064 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-09-08 02:12:47.064 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-09-08 02:12:47.064 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-09-08T02:12:47.064
2023-09-08 02:12:52.293 ziyun [http-nio-9528-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2023-09-08 02:12:52.293 ziyun [http-nio-9528-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2023-09-08 02:12:52.301 ziyun [http-nio-9528-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 8 ms
2023-09-08 02:14:19.903 ziyun [Thread-9] INFO  o.s.s.c.ThreadPoolTaskExecutor - Shutting down ExecutorService 'taskExecutor'
2023-09-08 02:14:19.904 ziyun [Thread-9] INFO  o.s.s.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2023-09-08 02:14:19.904 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1694110365146 shutting down.
2023-09-08 02:14:19.904 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1694110365146 paused.
2023-09-08 02:14:19.904 ziyun [Thread-9] INFO  org.quartz.core.QuartzScheduler - Scheduler MyScheduler_$_MSI1694110365146 shutdown complete.
2023-09-08 02:14:19.905 ziyun [Thread-9] INFO  c.b.d.d.DynamicRoutingDataSource - closing dynamicDatasource  ing....
2023-09-08 02:14:19.905 ziyun [Thread-9] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown initiated...
2023-09-08 02:14:19.908 ziyun [Thread-9] INFO  com.zaxxer.hikari.HikariDataSource - primary - Shutdown completed.
