2023-07-19 13:23:10.042 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Starting RepairSystemApplication on MSI with PID 14544 (started by KarlK<PERSON> in D:\project\Java Projectes\queuingsystem)
2023-07-19 13:23:10.043 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - No active profile set, falling back to default profiles: default
2023-07-19 13:23:10.063 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2023-07-19 13:23:10.063 ziyun [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2023-07-19 13:23:11.382 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2023-07-19 13:23:11.383 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2023-07-19 13:23:11.412 ziyun [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22ms. Found 0 repository interfaces.
2023-07-19 13:23:11.659 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$5c95acef] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:11.724 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:11.726 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$c6241dc0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:11.729 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:11.731 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDatasourceAnnotationAdvisor' of type [com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:11.731 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.ziyun.repairsystem.common.authentication.ShiroConfig$$EnhancerBySpringCGLIB$$b0cff5da] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:11.876 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisConfig' of type [com.ziyun.repairsystem.common.config.RedisConfig$$EnhancerBySpringCGLIB$$f0a90f2e] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:11.885 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisPoolFactory' of type [redis.clients.jedis.JedisPool] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:11.887 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'redisService' of type [com.ziyun.repairsystem.common.service.impl.RedisServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:11.921 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatis-plus-com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:11.925 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mybatisPlusConfig' of type [com.ziyun.repairsystem.common.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$ebe438c0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:11.927 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'paginationInterceptor' of type [com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:11.931 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration' of type [com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration$$EnhancerBySpringCGLIB$$9a5e1989] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:11.936 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceCreator' of type [com.baomidou.dynamic.datasource.DynamicDataSourceCreator] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:11.937 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dynamicDataSourceProvider' of type [com.baomidou.dynamic.datasource.provider.YmlDynamicDataSourceProvider] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:11.938 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-检测到并开启了p6spy
2023-07-19 13:23:11.957 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Starting...
2023-07-19 13:23:12.294 ziyun [restartedMain] INFO  com.zaxxer.hikari.HikariDataSource - primary - Start completed.
2023-07-19 13:23:12.294 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 初始共加载 1 个数据源
2023-07-19 13:23:12.294 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 动态数据源-加载 primary 成功
2023-07-19 13:23:12.294 ziyun [restartedMain] INFO  c.b.d.d.DynamicRoutingDataSource - 当前的默认数据源是单数据源，数据源名为 primary
2023-07-19 13:23:12.294 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dataSource' of type [com.baomidou.dynamic.datasource.DynamicRoutingDataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.301 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.305 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' of type [org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.760 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionFactory' of type [org.apache.ibatis.session.defaults.DefaultSqlSessionFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.764 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sqlSessionTemplate' of type [org.mybatis.spring.SqlSessionTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.766 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.767 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMapper' of type [com.sun.proxy.$Proxy117] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.783 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.787 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuMapper' of type [com.sun.proxy.$Proxy119] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.792 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.792 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleMapper' of type [com.sun.proxy.$Proxy120] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.793 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userRoleService' of type [com.ziyun.repairsystem.system.service.impl.UserRoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.811 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleMenuService' of type [com.ziyun.repairsystem.system.service.impl.RoleMenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.818 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'roleService' of type [com.ziyun.repairsystem.system.service.impl.RoleServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.832 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.833 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuMapper' of type [com.sun.proxy.$Proxy124] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.835 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'menuService' of type [com.ziyun.repairsystem.system.service.impl.MenuServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.845 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.845 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userMapper' of type [com.sun.proxy.$Proxy126] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.865 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [org.mybatis.spring.mapper.MapperFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.866 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigMapper' of type [com.sun.proxy.$Proxy127] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.868 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userConfigService' of type [com.ziyun.repairsystem.system.service.impl.UserConfigServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.878 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userService' of type [com.ziyun.repairsystem.system.service.impl.UserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.887 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration$$EnhancerBySpringCGLIB$$b8292c80] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.889 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$6a75ee99] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.890 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$$EnhancerBySpringCGLIB$$258cd488] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.895 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties' of type [org.springframework.boot.autoconfigure.jackson.JacksonProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.895 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'standardJacksonObjectMapperBuilderCustomizer' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration$StandardJackson2ObjectMapperBuilderCustomizer] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.898 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration$$EnhancerBySpringCGLIB$$4c584bb6] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.901 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'parameterNamesModule' of type [com.fasterxml.jackson.module.paramnames.ParameterNamesModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.902 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration' of type [org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$$EnhancerBySpringCGLIB$$9a34ca0f] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.906 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jsonComponentModule' of type [org.springframework.boot.jackson.JsonComponentModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.907 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.data.web.config.SpringDataJacksonConfiguration' of type [org.springframework.data.web.config.SpringDataJacksonConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.908 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonGeoModule' of type [org.springframework.data.geo.GeoModule] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.912 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapperBuilder' of type [org.springframework.http.converter.json.Jackson2ObjectMapperBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.923 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jacksonObjectMapper' of type [com.fasterxml.jackson.databind.ObjectMapper] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.930 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheService' of type [com.ziyun.repairsystem.common.service.impl.CacheServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.932 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userManager' of type [com.ziyun.repairsystem.system.manager.UserManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.933 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealm' of type [com.ziyun.repairsystem.common.authentication.ShiroRealm] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.936 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'securityManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:12.945 ziyun [restartedMain] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2023-07-19 13:23:13.169 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9528 (http)
2023-07-19 13:23:13.175 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9528"]
2023-07-19 13:23:13.180 ziyun [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-07-19 13:23:13.180 ziyun [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.19]
2023-07-19 13:23:13.242 ziyun [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-07-19 13:23:13.242 ziyun [restartedMain] INFO  o.s.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 3178 ms
2023-07-19 13:23:13.629 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2023-07-19 13:23:13.633 ziyun [restartedMain] INFO  o.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2023-07-19 13:23:13.633 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.1 created.
2023-07-19 13:23:13.636 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - Using db table-based data access locking (synchronization).
2023-07-19 13:23:13.637 ziyun [restartedMain] INFO  o.s.s.quartz.LocalDataSourceJobStore - JobStoreCMT initialized.
2023-07-19 13:23:13.637 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.1) 'MyScheduler' with instanceId 'MSI1689744193629'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

2023-07-19 13:23:13.637 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'MyScheduler' initialized from an externally provided properties instance.
2023-07-19 13:23:13.637 ziyun [restartedMain] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.1
2023-07-19 13:23:13.638 ziyun [restartedMain] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@2bc723
2023-07-19 13:23:13.668 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:13 | 耗时 12 ms | SQL 语句：
select job_id jobId, bean_name beanName, method_name methodName, params, cron_expression cronExpression, status, remark, create_time createTime from t_job order by job_id;
2023-07-19 13:23:14.163 ziyun [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 2 endpoint(s) beneath base path '/actuator'
2023-07-19 13:23:14.220 ziyun [restartedMain] INFO  o.s.s.c.ThreadPoolTaskExecutor - Initializing ExecutorService 'taskExecutor'
2023-07-19 13:23:14.238 ziyun [restartedMain] INFO  s.d.s.w.PropertySourcedRequestMappingHandlerMapping - Mapped URL path [/v2/api-docs] onto method [public org.springframework.http.ResponseEntity<springfox.documentation.spring.web.json.Json> springfox.documentation.swagger2.web.Swagger2Controller.getDocumentation(java.lang.String,javax.servlet.http.HttpServletRequest)]
2023-07-19 13:23:14.341 ziyun [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2023-07-19 13:23:14.711 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Context refreshed
2023-07-19 13:23:14.721 ziyun [restartedMain] INFO  s.d.s.w.p.DocumentationPluginsBootstrapper - Found 1 custom documentation plugin(s)
2023-07-19 13:23:14.744 ziyun [restartedMain] INFO  s.d.s.w.s.ApiListingReferenceScanner - Scanning for api listing references
2023-07-19 13:23:14.821 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_1
2023-07-19 13:23:14.828 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_2
2023-07-19 13:23:14.832 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_3
2023-07-19 13:23:14.835 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_4
2023-07-19 13:23:14.841 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_5
2023-07-19 13:23:14.866 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: addRepairInfoUsingPOST_1
2023-07-19 13:23:14.871 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_6
2023-07-19 13:23:14.877 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_7
2023-07-19 13:23:14.877 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: querySwjgRoUsingGET_1
2023-07-19 13:23:14.880 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_8
2023-07-19 13:23:14.888 ziyun [restartedMain] INFO  s.d.s.w.r.o.CachingOperationNameGenerator - Generating unique operation named: exportUsingPOST_9
2023-07-19 13:23:14.902 ziyun [restartedMain] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9528"]
2023-07-19 13:23:14.914 ziyun [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 9528 (http) with context path ''
2023-07-19 13:23:14.915 ziyun [restartedMain] INFO  c.z.r.RepairSystemApplication - Started RepairSystemApplication in 5.11 seconds (JVM running for 10.502)
2023-07-19 13:23:14.920 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - Redis连接中 ······
2023-07-19 13:23:14.930 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存初始化 ······
2023-07-19 13:23:14.931 ziyun [restartedMain] INFO  c.z.r.common.runner.CacheInitRunner - 缓存用户数据 ······
2023-07-19 13:23:14.970 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:14 | 耗时 13 ms | SQL 语句：
SELECT USER_ID,username,password,DEPT_ID,email,mobile,status,create_time,modify_time,last_login_time,ssex,description,REAL_NAME,avatar FROM t_user;
2023-07-19 13:23:14.986 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:14 | 耗时 13 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'ziyun' group by u.username;
2023-07-19 13:23:15.019 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'ziyun';
2023-07-19 13:23:15.033 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'ziyun' and m.perms is not null and m.perms <> '';
2023-07-19 13:23:15.052 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 12 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='166' ;
2023-07-19 13:23:15.065 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'test123' group by u.username;
2023-07-19 13:23:15.078 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 12 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'test123';
2023-07-19 13:23:15.090 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'test123' and m.perms is not null and m.perms <> '';
2023-07-19 13:23:15.102 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='167' ;
2023-07-19 13:23:15.115 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 12 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'yangyang' group by u.username;
2023-07-19 13:23:15.164 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 12 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'yangyang';
2023-07-19 13:23:15.176 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 12 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'yangyang' and m.perms is not null and m.perms <> '';
2023-07-19 13:23:15.190 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='171' ;
2023-07-19 13:23:15.203 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15858214004' group by u.username;
2023-07-19 13:23:15.216 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15858214004';
2023-07-19 13:23:15.231 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 13 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15858214004' and m.perms is not null and m.perms <> '';
2023-07-19 13:23:15.242 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 10 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='172' ;
2023-07-19 13:23:15.255 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'realwjy001' group by u.username;
2023-07-19 13:23:15.276 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'realwjy001';
2023-07-19 13:23:15.289 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'realwjy001' and m.perms is not null and m.perms <> '';
2023-07-19 13:23:15.302 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 12 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='174' ;
2023-07-19 13:23:15.315 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 13 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15067108130' group by u.username;
2023-07-19 13:23:15.331 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 13 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15067108130';
2023-07-19 13:23:15.344 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 13 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15067108130' and m.perms is not null and m.perms <> '';
2023-07-19 13:23:15.356 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='175' ;
2023-07-19 13:23:15.369 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 13 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15755998467' group by u.username;
2023-07-19 13:23:15.382 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15755998467';
2023-07-19 13:23:15.395 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15755998467' and m.perms is not null and m.perms <> '';
2023-07-19 13:23:15.406 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='176' ;
2023-07-19 13:23:15.419 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 12 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'qita' group by u.username;
2023-07-19 13:23:15.431 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 10 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'qita';
2023-07-19 13:23:15.443 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'qita' and m.perms is not null and m.perms <> '';
2023-07-19 13:23:15.453 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 10 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='177' ;
2023-07-19 13:23:15.467 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15869119455' group by u.username;
2023-07-19 13:23:15.481 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 13 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15869119455';
2023-07-19 13:23:15.492 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15869119455' and m.perms is not null and m.perms <> '';
2023-07-19 13:23:15.503 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='178' ;
2023-07-19 13:23:15.517 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 12 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = '15868125270' group by u.username;
2023-07-19 13:23:15.531 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 12 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = '15868125270';
2023-07-19 13:23:15.545 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 12 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = '15868125270' and m.perms is not null and m.perms <> '';
2023-07-19 13:23:15.556 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 10 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='179' ;
2023-07-19 13:23:15.569 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 12 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'wangliwei' group by u.username;
2023-07-19 13:23:15.583 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 12 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'wangliwei';
2023-07-19 13:23:15.596 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'wangliwei' and m.perms is not null and m.perms <> '';
2023-07-19 13:23:15.612 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 12 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='180' ;
2023-07-19 13:23:15.625 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'daiwei' group by u.username;
2023-07-19 13:23:15.638 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'daiwei';
2023-07-19 13:23:15.655 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 13 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'daiwei' and m.perms is not null and m.perms <> '';
2023-07-19 13:23:15.668 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='181' ;
2023-07-19 13:23:15.681 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
SELECT u.user_id userId, u.username, u.password, u.email, u.mobile, u. STATUS, u.create_time createTime, u.ssex, d.dept_id deptId, d.dept_name deptName, u.AVATAR, u.DESCRIPTION, u.real_name, u.LAST_LOGIN_TIME lastLoginTime, GROUP_CONCAT(r.role_id) roleId, GROUP_CONCAT(r.ROLE_NAME) roleName FROM t_user u LEFT JOIN t_dept d ON (u.dept_id = d.dept_id) LEFT JOIN t_user_role ur ON (u.user_id = ur.user_id) LEFT JOIN t_role r ON r.role_id = ur.role_id WHERE u.username = 'phonedispose' group by u.username;
2023-07-19 13:23:15.695 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 12 ms | SQL 语句：
select r.* from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) where u.username = 'phonedispose';
2023-07-19 13:23:15.706 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 11 ms | SQL 语句：
select distinct m.perms from t_role r left join t_user_role ur on (r.role_id = ur.role_id) left join t_user u on (u.user_id = ur.user_id) left join t_role_menu rm on (rm.role_id = r.role_id) left join t_menu m on (m.menu_id = rm.menu_id) where u.username = 'phonedispose' and m.perms is not null and m.perms <> '';
2023-07-19 13:23:15.718 ziyun [restartedMain] INFO  p6spy - 2023-07-19 13:23:15 | 耗时 10 ms | SQL 语句：
SELECT USER_ID,theme,layout,multi_page,fix_siderbar,fix_header,color FROM t_user_config WHERE USER_ID='182' ;
2023-07-19 13:23:15.718 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -  __    ___   _      ___   _     ____ _____  ____ 
2023-07-19 13:23:15.718 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - / /`  / / \ | |\/| | |_) | |   | |_   | |  | |_  
2023-07-19 13:23:15.718 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - \_\_, \_\_/ |_|  | |_|   |_|__ |_|__  |_|  |_|__ 
2023-07-19 13:23:15.718 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner -                                                       
2023-07-19 13:23:15.718 ziyun [restartedMain] INFO  c.z.r.common.runner.StartedUpRunner - ZiYun 启动完毕，时间：2023-07-19T13:23:15.718
