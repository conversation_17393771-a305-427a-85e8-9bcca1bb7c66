server:
  port: 9528
  undertow:
    # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
    io-threads: 4
    # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
    worker-threads: 20
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 1024
    # 是否分配的直接内存
    direct-buffers: true

#  port: 3366
spring:
  datasource:
    dynamic:
      p6spy: true
      hikari:
        connection-timeout: 30000
        max-lifetime: 1800000
        max-pool-size: 15
        min-idle: 5
        connection-test-query: select 1
        pool-name: JgzyHikariCP
      primary: primary
      datasource:
        primary:
          username: JgzY
          password: JgzY@^&-)XswL
#          username: root
#          password: root
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: jdbc:mysql://************:6330/repairsystem?useUnicode=true&characterEncoding=UTF-8&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=GMT%2b8&allowPublicKeyRetrieval=true
#          url: ***********************************************************************************************************************************************************************************************
  aop:
    proxy-target-class: true

  messages:
    encoding: utf-8

  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

  redis:
    host: 127.0.0.1
    port: 6379
#    password: jgzy#5359
    password:
    jedis:
      pool:
        min-idle: 8
        max-idle: 20
        max-active: 8
        max-wait: 3000
    timeout: 3000


mybatis-plus:
  type-aliases-package: com.ziyun.repairsystem.system.domain,com.ziyun.repairsystem.job.domain,com.ziyun.repairsystem.repair.domain
  mapper-locations: classpath:mapper/*/*.xml
  configuration:
    jdbc-type-for-null: null
  global-config:
    banner: false

management:
  endpoints: 
    web:
      exposure:
        include: [ 'httptrace', 'metrics' ]


ziyun:
  openAopLog: true
  max:
    batch:
      insert:
        num: 1000
  shiro:
    anonUrl: /login,/logout/**,/regist,/user/check/**,/web/**,/**.jpg,/repair/**,/sms/**
    jwtTimeOut: 3600

qiniu:
  accessKey: m8GbhhC6vKrWif1Qqx6488RKkwD9vIk9peVMumps
  secretKey: s7xgFot5fLVCV2fV0DDYg3mlEgZkc-fs9BpG83gH
  bucket: mytest
  bucketUrl: image.queuingsystem.cn

dingtalk:
  agentId: 2774205110
  appKey: dingffflvl1akohcttrl
  appSecret: YcrR7P220apB3p1xlm6roP3qDK_4o27EdSo8TfQOQxB1Qcs1u992ZPay3nDV2wD7

sms:
  product: Dysmsapi
  domain: dysmsapi.aliyuncs.com
  accessKeyId: LTAIAOE6Z8j3RQkd
  accessKeySecret: jG6bo25ocdrEB5yCx06zYh9d4qoha1
  signName: 紫云服务
  smscode_1: SMS_137416545
  smscode_2: SMS_137426632
  smscode_3: SMS_137426661
  smscode_4: SMS_205128713
  smscode_5: SMS_205139194
  smscode_6: SMS_205126855
  smscode_7: SMS_205128709
  smscode_8: SMS_219625650
  phone-numbers: "[\"19906512094\",\"19906512420\"]"
#  phone-numbers: "[\"15382312786\",\"13989881543\"]"