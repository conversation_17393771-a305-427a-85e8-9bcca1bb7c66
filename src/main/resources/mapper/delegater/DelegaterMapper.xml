<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ziyun.repairsystem.repair.dao.DelegaterMapper">

    <!-- 映射DelegaterVo对象 -->
    <resultMap id="delegaterResultMap" type="com.ziyun.repairsystem.repair.domain.vo.DelegaterVo">
        <id column="id" property="id"/>
        <result column="delegater_name" property="name"/>
        <result column="company_name" property="companyName"/>
        <result column="pay_account" property="payAccount"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="section" property="section"/>

        <!-- 映射List -->
        <collection property="unitList" ofType="com.ziyun.repairsystem.repair.domain.vo.DelegaterDistrictVo">
            <result column="BSDTMC" property="label"/>
            <result column="BSDTDJXH" property="value"/>
        </collection>
    </resultMap>


    <select id="selectDelegater" parameterType="com.ziyun.repairsystem.repair.domain.vo.DelegaterVo">
        SELECT id
        FROM t_delegater
        WHERE
        <if test="name != null">
            delegater_name = #{name}
        </if>
        <if test="companyName != null">
            AND company_name = #{age}
        </if>
        <if test="payAccount != null">
            AND pay_account = #{payAccount}
        </if>
        <if test="phoneNumber != null">
            AND phone_number = #{phoneNumber}
        </if>
        <if test="section != null">
            AND section = #{section}
        </if>
    </select>

    <!-- 查询 -->
    <select id="selectDelegaterById" resultMap="delegaterResultMap">
        SELECT d.id,d.company_name,d.pay_account,d.phone_number,d.delegater_name,d.section,td.BSDTMC,td.BSDTDJXH
        FROM t_delegater d
            LEFT JOIN t_delegater_district dd ON d.id = dd.delegater_id
            LEFT JOIN t_swjg_info td ON dd.bsdtdjxh = td.BSDTDJXH
        WHERE d.id = #{id}
    </select>

    <select id="selectAllDelegater" resultMap="delegaterResultMap">
        SELECT d.id,d.company_name,d.pay_account,d.phone_number,d.delegater_name,d.section,td.BSDTMC,td.BSDTDJXH
        FROM t_delegater d
        LEFT JOIN t_delegater_district dd ON d.id = dd.delegater_id
        LEFT JOIN t_swjg_info td ON dd.bsdtdjxh = td.BSDTDJXH
    </select>

    <!-- 插入DelegaterVo对象 -->
    <insert id="insertDelegater" parameterType="com.ziyun.repairsystem.repair.domain.vo.DelegaterVo">
        INSERT INTO t_delegater (delegater_name, company_name, pay_account, phone_number, section)
        VALUES (#{name}, #{companyName}, #{payAccount}, #{phoneNumber}, #{section})
    </insert>
    <!-- 更新DelegaterVo对象 -->
    <update id="updateDelegater" parameterType="com.ziyun.repairsystem.repair.domain.vo.DelegaterVo">
        UPDATE t_delegater
        SET delegater_name = #{name}, company_name = #{companyName}, pay_account = #{payAccount},
            phone_number = #{phoneNumber}, section = #{section}
        WHERE id = #{id}
    </update>

    <!-- 删除DelegaterDistrictVo对象 -->
    <delete id="deleteDelegaterDistricts" parameterType="java.lang.Integer">
        DELETE FROM t_delegater_district
        WHERE delegater_id = #{delegaterId}
    </delete>

    <!-- 插入DelegaterDistrictVo对象 -->
    <insert id="insertDelegaterDistrict" parameterType="com.ziyun.repairsystem.repair.domain.DelegaterDistrict">
        INSERT INTO t_delegater_district (delegater_id, bsdtdjxh)
        VALUES (#{delegaterId}, #{bsdtdjxh})
    </insert>


</mapper>