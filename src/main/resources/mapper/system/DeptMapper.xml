<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ziyun.repairsystem.system.dao.DeptMapper">
    <!-- 递归删除菜单，findDeptChildren为自定义的 MySQL函数，作用为根据当前 deptId递归查找出其所有下级部门 -->
    <delete id="deleteDepts" parameterType="string">
        DELETE
        FROM
            t_dept
        WHERE
            dept_id IN ( SELECT d.dept_id FROM ( SELECT dept_id FROM t_dept WHERE FIND_IN_SET( dept_id, findDeptChildren ( #{deptId} ) ) > 0 ) d )
    </delete>
</mapper>