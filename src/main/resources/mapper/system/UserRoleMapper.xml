<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ziyun.repairsystem.system.dao.UserRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ziyun.repairsystem.system.domain.UserRole">
        <id column="user_id" property="userId"/>
        <result column="role_id" property="roleId"/>
    </resultMap>

    <!--根据用户Id删除该用户的角色关系-->
    <delete id="deleteByUserId">
		DELETE FROM t_user_role WHERE user_id = #{userId}
	</delete>

    <!--根据角色Id删除该角色的用户关系-->
    <delete id="deleteByRoleId">
		DELETE FROM t_user_role WHERE role_id = #{roleId}
	</delete>

</mapper>
