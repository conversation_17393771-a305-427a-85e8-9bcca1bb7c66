<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ziyun.repairsystem.repair.dao.RepairInfoMapper">
    <resultMap id="BaseResultMap" type="com.ziyun.repairsystem.repair.domain.RepairInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="REPAIR_ID" jdbcType="BIGINT" property="repairId"/>
        <result column="NAME" jdbcType="VARCHAR" property="name"/>
        <result column="MOBILE" jdbcType="VARCHAR" property="mobile"/>
        <result column="TITLE" jdbcType="VARCHAR" property="title"/>
        <result column="ADDRESS" jdbcType="VARCHAR" property="address"/>
        <result column="REVIEWS" jdbcType="VARCHAR" property="reviews"/>
        <result column="STATE_TAG" jdbcType="VARCHAR" property="stateTag"/>
        <result column="REPAIR_NUMBER" jdbcType="VARCHAR" property="repairNumber"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="REVIEW_TIME" jdbcType="TIMESTAMP" property="reviewTime"/>
        <result column="DEAL_TIME" jdbcType="TIMESTAMP" property="dealTime"/>
        <result column="PARTIAL_COMPLETION_TIME" jdbcType="TIMESTAMP" property="partialCompletionTime"/>
        <result column="COMPLETION_TIME" jdbcType="TIMESTAMP" property="completionTime"/>
        <result column="HANDLE_CONT" jdbcType="LONGVARCHAR" property="handleCont"/>
        <result column="CONT" jdbcType="LONGVARCHAR" property="cont"/>
        <result column="IMG" jdbcType="LONGVARCHAR" property="img"/>
        <result column="SWJGDM" jdbcType="VARCHAR" property="swjgdm"/>
        <result column="BSDTMC" jdbcType="VARCHAR" property="bsdtmc"/>
        <result column="USER_ID" jdbcType="BIGINT" property="userId"/>
        <result column="REAL_NAME" jdbcType="VARCHAR" property="realname"/>
        <result column="RECEIPT" jdbcType="BIGINT" property="receipt"/>
        <result column ="PROCESSOR_ID" jdbcType="DECIMAL" property="processor_id"/>
    </resultMap>

</mapper>
