<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ziyun.repairsystem.repair.dao.DistrictMapper">
    <resultMap id="BaseResultMap" type="com.ziyun.repairsystem.repair.domain.District">
        <id column="district_id" jdbcType="SMALLINT" property="districtId"/>
        <result column="pid" jdbcType="SMALLINT" property="pid"/>
        <result column="district" jdbcType="VARCHAR" property="district"/>
        <result column="level" jdbcType="BIT" property="level"/>
    </resultMap>
</mapper>
