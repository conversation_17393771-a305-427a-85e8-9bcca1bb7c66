<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ziyun.repairsystem.repair.dao.StaffRepairInfoMapper">
  <resultMap id="BaseResultMap" type="com.ziyun.repairsystem.repair.domain.StaffRepairInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="REPAIR_ID" jdbcType="BIGINT" property="repairId" />
    <result column="STAFF_ID" jdbcType="BIGINT" property="staffId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="ADD_USER" jdbcType="VARCHAR" property="addUser" />
  </resultMap>
</mapper>