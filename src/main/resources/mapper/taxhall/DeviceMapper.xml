<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ziyun.repairsystem.taxhall.dao.DeviceMapper">

    <resultMap id="BaseResultMap" type="com.ziyun.repairsystem.taxhall.domain.Device">
        <id column="ID" jdbcType="BIGINT" property="Id"/>
        <result column="DEVICE_NAME" jdbcType="VARCHAR" property="deviceName"/>
        <result column="DEVICE_DESCRIPTION" jdbcType="LONGVARCHAR" property="deviceDescription"/>
        <result column="IP" jdbcType="VARCHAR" property="ip"/>
        <result column="TAXHALL_ID" jdbcType="VARCHAR" property="taxhallId"/>
    </resultMap>
</mapper>
