<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ziyun.repairsystem.taxhall.dao.SwjgInfoMapper">
    <resultMap id="BaseResultMap" type="com.ziyun.repairsystem.taxhall.domain.SwjgInfo">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="SWJG_ID" jdbcType="BIGINT" property="swjgId" />
        <result column="BSDTDJXH" jdbcType="VARCHAR" property="bsdtdjxh" />
        <result column="SWJGDM" jdbcType="VARCHAR" property="swjgdm" />
        <result column="SWJGMC" jdbcType="VARCHAR" property="swjgmc" />
        <result column="BSDTMC" jdbcType="VARCHAR" property="bsdtmc" />
        <result column="SWJGDSMC" jdbcType="VARCHAR" property="swjgdsmc" />
        <result column="SWJGDZ" jdbcType="VARCHAR" property="swjgdz" />
        <result column="DISTRICT_ID" jdbcType="VARCHAR" property="districtId" />
        <result column="DISTRICT_ID" jdbcType="VARCHAR" property="districtId" />
        <result column="CONTACT_PERSON" jdbcType="VARCHAR" property="contactPerson" />
        <result column="TEL" jdbcType="VARCHAR" property="tel" />
    </resultMap>
</mapper>
