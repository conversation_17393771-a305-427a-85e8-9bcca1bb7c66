package com.ziyun.repairsystem.system.controller;

import com.aliyuncs.dysmsapi.model.v20170525.SendBatchSmsResponse;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.ziyun.repairsystem.common.domain.ResponseBo;
import com.ziyun.repairsystem.common.domain.SmsProperties;
import com.ziyun.repairsystem.common.domain.SmsType;
import com.ziyun.repairsystem.common.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: ytx
 * @Date: 2020/10/23  14:41
 */
@Slf4j
@CrossOrigin
@RestController
public class SmsController {
    @Autowired
    private SmsProperties smsProperties;
    @Autowired
    private SmsService smsService;

    /**
     * 短信通知
     *
     * @param reMap
     * @return
     * @throws Exception
     */
    @PostMapping("sms/notify")
    public ResponseBo SmsNotify(@RequestParam Map<String, String> reMap) throws ClientException {
        Map<String, String> batchMap = new HashMap<>();
        try {
            log.info("SMS: "+reMap.toString());
            if (StringUtils.isNotBlank(reMap.get("flag"))) {
                if (Integer.valueOf(reMap.get("flag")) == 0) {
                    //提醒短信通知
                    batchMap.put("taxhall", reMap.get("taxhall"));
                    batchMap.put("phone_num", reMap.get("phone_num"));
                    batchMap.put("tkt_id", reMap.get("tkt_id"));
                    batchMap.put("biz_name", reMap.get("biz_name") + ",");
                    if (Integer.valueOf(reMap.get("remind_num")) - 1 <= 0) {
                        batchMap.put("status", "0");
                    } else {
                        batchMap.put("status", Integer.valueOf(Integer.valueOf(reMap.get("remind_num")) - 1).toString());
                    }
                    SendSmsResponse sendSmsResponse = smsService.sendSms(smsProperties.getSmscode_5(), batchMap);
                    return sendSmsResponse.getCode().equals("OK") ? ResponseBo.ok("短信发送成功!") : ResponseBo.error("短信发送失败：" + sendSmsResponse.getMessage());
                } else if (Integer.valueOf(reMap.get("flag")) == 3) {
                    //呼叫短信通知
                    batchMap.put("taxhall", reMap.get("taxhall"));
                    batchMap.put("phone_num", reMap.get("phone_num"));
                    batchMap.put("tkt_id", reMap.get("tkt_id"));
                    batchMap.put("biz_name", reMap.get("biz_name"));
                    batchMap.put("win_name", reMap.get("win_name"));
                    SendSmsResponse sendSmsResponse = smsService.sendSms(smsProperties.getSmscode_4(), batchMap);
                    return sendSmsResponse.getCode().equals("OK") ? ResponseBo.ok("短信发送成功!") : ResponseBo.error("短信发送失败：" + sendSmsResponse.getMessage());
                } else if (Integer.valueOf(reMap.get("flag")) == 5) {
                    //办结短信通知
                    batchMap.put("taxhall", reMap.get("taxhall"));
                    batchMap.put("phone_num", reMap.get("phone_num"));
                    SendSmsResponse sendSmsResponse = smsService.sendSms(smsProperties.getSmscode_6(), batchMap);
                    return sendSmsResponse.getCode().equals("OK") ? ResponseBo.ok("短信发送成功!") : ResponseBo.error("短信发送失败：" + sendSmsResponse.getMessage());
                } else if (Integer.valueOf(reMap.get("flag")) == 99) {
                    //取号成功提醒短信通知短信
                    batchMap.put("taxhall", reMap.get("taxhall"));
                    batchMap.put("phone_num", reMap.get("phone_num"));
                    batchMap.put("tkt_id", reMap.get("tkt_id"));
                    batchMap.put("biz_name", reMap.get("biz_name"));
                    if (Integer.valueOf(reMap.get("remind_num")) - 1 <= 0) {
                        batchMap.put("status", "0");
                    } else {
                        batchMap.put("status", Integer.valueOf(Integer.valueOf(reMap.get("remind_num")) - 1).toString());
                    }
//                    batchMap.put("status", reMap.get("remind_num"));
                    SendSmsResponse sendSmsResponse = smsService.sendSms(smsProperties.getSmscode_7(), batchMap);
                    return sendSmsResponse.getCode().equals("OK") ? ResponseBo.ok("短信发送成功!") : ResponseBo.error("短信发送失败：" + sendSmsResponse.getMessage());
                }
            }else if(StringUtils.isNotBlank(reMap.get("out"))){
               log.info("接受到来自内网outTest的请求");
               return ResponseBo.ok("接收到来自outTest的请求");
            }
            return ResponseBo.error("flag参数错误!");
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseBo.error("发送失败!");
        }
    }
//    /**
//     * 短信通知
//     *
//     * @param reMap
//     * @return
//     * @throws Exception
//     */
//    @PostMapping("sms/notify")
//    public ResponseBo SmsNotify(@RequestParam Map<String, String> reMap) throws ClientException {
//        try {
//            log.info(reMap.toString());
//
//            if (StringUtils.isNotBlank(reMap.get("flag"))) {
//                int flag = Integer.parseInt(reMap.get("flag"));
//
//                Map<String, String> batchMap = new HashMap<>();
//                String smsCode = null;
//
//                switch (flag) {
//                    case 0:
//                        // 提醒短信通知
//                        batchMap.put("taxhall", reMap.get("taxhall"));
//                        batchMap.put("phone_num", reMap.get("phone_num"));
//                        batchMap.put("tkt_id", reMap.get("tkt_id"));
//                        batchMap.put("biz_name", reMap.get("biz_name") + ",");
////                        int remindNum = Integer.parseInt(reMap.get("remind_num"));
////                        if (remindNum <= 1) {
////                            batchMap.put("status", "0");
////                        } else {
////                            batchMap.put("status", String.valueOf(remindNum - 1));
////                        }
//                        batchMap.put("status", Integer.toString(Math.max(Integer.parseInt(reMap.get("remind_num")) - 1, 0)));
//                        smsCode = smsProperties.getSmscode_5();
//                        break;
//                    case 3:
//                        // 呼叫短信通知
//                        batchMap.put("taxhall", reMap.get("taxhall"));
//                        batchMap.put("phone_num", reMap.get("phone_num"));
//                        batchMap.put("tkt_id", reMap.get("tkt_id"));
//                        batchMap.put("biz_name", reMap.get("biz_name"));
//                        batchMap.put("win_name", reMap.get("win_name"));
//                        smsCode = smsProperties.getSmscode_4();
//                        break;
//                    case 5:
//                        // 办结短信通知
//                        batchMap.put("taxhall", reMap.get("taxhall"));
//                        batchMap.put("phone_num", reMap.get("phone_num"));
//                        smsCode = smsProperties.getSmscode_6();
//                        break;
//                    case 99:
//                        // 取号成功提醒短信通知短信
//                        batchMap.put("taxhall", reMap.get("taxhall"));
//                        batchMap.put("phone_num", reMap.get("phone_num"));
//                        batchMap.put("tkt_id", reMap.get("tkt_id"));
//                        batchMap.put("biz_name", reMap.get("biz_name"));
//                        batchMap.put("status", Integer.toString(Math.max(Integer.parseInt(reMap.get("remind_num")) - 1, 0)));
//                        smsCode = smsProperties.getSmscode_7();
//                        break;
//                    default:
//                        return ResponseBo.error("flag参数错误!");
//                }
//
//                SendSmsResponse sendSmsResponse = smsService.sendSms(smsCode, batchMap);
//                return sendSmsResponse.getCode().equals("OK") ? ResponseBo.ok("短信发送成功!") : ResponseBo.error("短信发送失败：" + sendSmsResponse.getMessage());
//            } else if(StringUtils.isNotBlank(reMap.get("out"))) {
//                log.info("接受到来自内网outTest的请求");
//                return ResponseBo.ok("接收到来自outTest的请求");
//            }
//            return ResponseBo.error("flag参数错误!");
//        } catch (Exception e) {
//            e.printStackTrace();
//            return ResponseBo.error("发送失败!");
//        }
//    }

}