package com.ziyun.repairsystem.analysis.dao;

import com.ziyun.repairsystem.analysis.domain.*;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface RepairAnalysisMapper {

    /**统计本月报障数量：从当月1日起至统计日的所有报障数量
     *
     */
    @Select("SELECT COUNT(*) as count\n" +
            "FROM t_repair_info\n" +
            "WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= NOW();")
     RepairAnalysisCount queryAnalysisByMonthNow();

    /**
     *统计今日报障数量：从当日0时起至发送请求时的所有报障数量
     */
    @Select("SELECT COUNT(*) as count\n" +
            "FROM t_repair_info\n" +
            "WHERE ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();")
    RepairAnalysisCount queryAnalysisByDay();

    /**
     * 统计今日未审核报障数量：从当日0时起至发送请求时所有状态为未审核的报障数量
     */
    @Select("SELECT COUNT(*) as count\n" +
            "FROM t_repair_info\n" +
            "WHERE STATE_TAG = '0' and ADD_TIME >= CURDATE() AND ADD_TIME <= NOW();")
    RepairAnalysisCount queryAnalysisByDayUnreviewed();

    /**
     * 按年统计：报障平台所有数据按年统计数量
     */
    @Select("SELECT YEAR(ADD_TIME) as xAxis, COUNT(*) as count\n" +
            "FROM t_repair_info\n" +
            "GROUP BY YEAR(ADD_TIME);")
    List<RepairAnalysisChart> queryAnalysisAllYear();

    /**
     * 按月统计：发送请求时的年份从1月开始的按月统计的数量
     */
    @Select("SELECT DATE_FORMAT(ADD_TIME, '%m') AS xAxis, count(*) as count\n" +
            "FROM t_repair_info\n" +
            "WHERE DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())\n" +
            "GROUP BY xAxis\n" +
            "ORDER BY xAxis;")
    List<RepairAnalysisChart> queryAnalysisByYear();

    /**
     * 按日统计：发送请求时的月份从1号到发送请求当日的按日统计的数量
     * @return
     */
    @Select("SELECT DATE_FORMAT(ADD_TIME, '%d') AS xAxis, count(*) as count\n" +
            "FROM t_repair_info\n" +
            "WHERE ADD_TIME >= DATE_FORMAT(CURDATE(), '%Y-%m-01') AND ADD_TIME <= CURDATE()\n" +
            "GROUP BY xAxis\n" +
            "ORDER BY xAxis;")
    List<RepairAnalysisChart> queryAnalysisByMonth();

    @Select("/**\n" +
            "  上门服务\n" +
            " */\n" +
            "(select COUNT(*) AS value, '上门处理' AS name from t_repair_info r\n" +
            "where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.DEAL_TYPE = 0 and r.STATE_TAG = 4)\n" +
            "/**\n" +
            "  电话指导处理\n" +
            " */\n" +
            "UNION\n" +
            "(select COUNT(*) AS value, '电话指导处理' AS name from t_repair_info r\n" +
            "where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.DEAL_TYPE = 1 and r.STATE_TAG = 4)\n" +
            "/**\n" +
            "  远程处理\n" +
            " */\n" +
            "UNION\n" +
            "(select COUNT(*) AS value, '省局平台处理' AS name from t_repair_info r\n" +
            "where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.DEAL_TYPE = 2 and r.STATE_TAG = 4)\n" +
            "/**\n" +
            "代维处理\n" +
            " */\n" +
            "UNION\n" +
            "(select COUNT(*) AS value, '代维处理' AS name from t_repair_info r\n" +
            "where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.DEAL_TYPE = 3 and r.STATE_TAG = 4)\n" +
            "/**\n" +
            "未处理\n" +
            " */\n" +
            "UNION\n" +
            "(select COUNT(*) AS value, '未处理' AS name from t_repair_info r\n" +
            "WHERE DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.STATE_TAG >=1 and r.STATE_TAG < 4);")
    List<RepairAnalysisPie> queryAnalysisAllProcessDealType();

//    @Select("select COUNT(*) AS value, '上门处理' AS name from t_repair_info r where USER_ID = PROCESSOR_ID and r.USER_ID = #{userId} and r.STATE_TAG = 4\n" +
//            "UNION\n" +
//            "(select COUNT(*) AS value, '电话指导处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID\n" +
//            "where r.PROCESSOR_ID = 182 and r.USER_ID = #{userId} and r.STATE_TAG = 4)\n" +
//            "UNION\n" +
//            "(select COUNT(*) AS value, '省局平台处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID\n" +
//            "where r.PROCESSOR_ID = 180 and r.USER_ID = #{userId} and r.STATE_TAG = 4)\n" +
//            "UNION\n" +
//            "(select COUNT(*) AS value, '代维处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID\n" +
//            "where r.PROCESSOR_ID = 181 and r.USER_ID = #{userId} and r.STATE_TAG = 4);")
    @Select("(select COUNT(*) AS value, '上门处理' AS name from t_repair_info r where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.DEAL_TYPE = 0 and r.USER_ID = #{userId} and r.STATE_TAG = 4)\n" +
            "UNION\n" +
            "(select COUNT(*) AS value, '电话指导处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID\n" +
            "where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.DEAL_TYPE = 1 and r.USER_ID = #{userId} and r.STATE_TAG = 4)\n" +
            "UNION\n" +
            "(select COUNT(*) AS value, '省局平台处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID\n" +
            "where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.DEAL_TYPE = 2 and r.USER_ID = #{userId} and r.STATE_TAG = 4)\n" +
            "UNION\n" +
            "(select COUNT(*) AS value, '代维处理' AS name from t_repair_info r JOIN t_user u on u.USER_ID = r.PROCESSOR_ID\n" +
            "where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.DEAL_TYPE = 3 and r.USER_ID = #{userId} and r.STATE_TAG = 4)\n" +
            "UNION\n" +
            "(select COUNT(*) AS value, '未处理' AS name from t_repair_info r\n" +
            "WHERE DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW()) and r.USER_ID = #{userId} and r.STATE_TAG >=1 and r.STATE_TAG < 4);")
    List<RepairAnalysisPie> queryAnalysisByProcessDealType(Long userId);


    @Select("select COALESCE(count(STATE_TAG), 0) value, tmp_status.STATUS_TAG AS STATUS_TAG\n" +
            "from (select REPAIR_ID, STATE_TAG, ADD_TIME, r.SWJGDM, BSDTDJXH, s.DISTRICT_ID, district\n" +
            "      from t_repair_info r\n" +
            "               JOIN\n" +
            "           t_swjg_info s ON r.SWJGDM = s.BSDTDJXH\n" +
            "               JOIN\n" +
            "           (SELECT d2.district_id, d2.district\n" +
            "            FROM t_district d1\n" +
            "                     JOIN t_district d2 ON d1.district_id = d2.pid\n" +
            "            /**\n" +
            "            传入市的id\n" +
            "            */\n" +
            "            WHERE d1.district_id = #{id}) all_districts on s.DISTRICT_ID = all_districts.district_id where DATE_FORMAT(ADD_TIME, '%Y') = YEAR(NOW())) tmp_repair\n" +
            "         RIGHT JOIN\n" +
            "     (select t_dict.KEYY AS STATUS_TAG from t_dict where FIELD_NAME = 'status_tag') tmp_status\n" +
            "     on tmp_repair.STATE_TAG = tmp_status.STATUS_TAG\n" +
            "group by tmp_status.STATUS_TAG\n" +
            "order by tmp_status.STATUS_TAG;")
    List<RepairAnalysisSection> queryAnalysisBySection(Integer id);

    @Select("SELECT t_user.REAL_NAME AS xAxis, COUNT(*) AS count\n" +
            "FROM t_user\n" +
            "LEFT JOIN t_repair_info ON t_user.USER_ID = t_repair_info.PROCESSOR_ID\n" +
            "WHERE YEAR(t_repair_info.ADD_TIME) = YEAR(NOW())\n" +
            "GROUP BY t_user.USER_ID;")
    List<RepairAnalysisChart> queryAnalysisByProcessor();
}
