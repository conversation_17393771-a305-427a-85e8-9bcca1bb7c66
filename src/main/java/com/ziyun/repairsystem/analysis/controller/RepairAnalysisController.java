package com.ziyun.repairsystem.analysis.controller;


import com.ziyun.repairsystem.analysis.service.RepairAnalysisSrvice;
import com.ziyun.repairsystem.common.domain.ResponseBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@CrossOrigin
@RestController
@RequestMapping("repair-analysis")
public class RepairAnalysisController {

    @Autowired
    private RepairAnalysisSrvice repairAnalysisSrvice;

    @GetMapping("/base-count")
    @ResponseBody
    public Map<String, Object> analysisBaseCount() {
        Map<String, Object> reMap  = repairAnalysisSrvice.queryAnalysisCount();
        return reMap;
    }


    @GetMapping("/deal-type-count")
    @ResponseBody
    public ResponseBo analysisByDealType(@RequestParam(value="id",required = false) String id) {
//        Map<String,Object> reMap =  repairAnalysisSrvice.queryAnalysisByProcessType();
        return ResponseBo.ok(repairAnalysisSrvice.queryAnalysisByDealType(id));
    }

    @GetMapping("/time-deal-count")
    @ResponseBody
    public ResponseBo analysisDealCountByTime(@RequestParam(value="type",required = false) String type) {
        Map<String,Object> reMap =  repairAnalysisSrvice.queryAnalysisByType(type);
        return ResponseBo.ok(reMap);
    }

    @GetMapping("/section-deal-count")
    @ResponseBody
    public ResponseBo analysisDealCountBySection() {
        Map<String,Object> reMap =  repairAnalysisSrvice.queryAnalysisBySection();
        return ResponseBo.ok(reMap);
    }

    @GetMapping("/processor-count")
    @ResponseBody
    public ResponseBo analysisByProcessor() {
        Map<String,Object> reMap =  repairAnalysisSrvice.queryAnalysisByProcessor();
        return ResponseBo.ok(reMap);
    }
}
