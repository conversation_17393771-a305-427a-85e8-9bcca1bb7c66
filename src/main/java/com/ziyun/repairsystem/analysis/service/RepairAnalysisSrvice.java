package com.ziyun.repairsystem.analysis.service;

import com.ziyun.repairsystem.analysis.domain.RepairAnalysis;
import com.ziyun.repairsystem.analysis.domain.RepairAnalysisChart;
import com.ziyun.repairsystem.analysis.domain.RepairAnalysisCount;
import com.ziyun.repairsystem.analysis.domain.RepairAnalysisPie;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

public interface RepairAnalysisSrvice {

    /**
     *统计本月报障数量：从当月1日起至统计日的所有报障数量
     */
    Map<String,Object> queryAnalysisCount();

    /**
     * 查询分析饼图
     * @param type
     * @return
     */
    Map<String,Object> queryAnalysisByType(String type);
    /**
     * 根据传入type类型返回报障总数统计柱状图数据
     */
    List<RepairAnalysisPie> queryAnalysisByDealType(String userId);

    /**
     * 根据区域查询统计保障单
     * @return
     */
    Map<String,Object> queryAnalysisBySection();

    /**
     * 根据不同处理人统计今年报障数据，
     */
    Map<String,Object> queryAnalysisByProcessor();

}
