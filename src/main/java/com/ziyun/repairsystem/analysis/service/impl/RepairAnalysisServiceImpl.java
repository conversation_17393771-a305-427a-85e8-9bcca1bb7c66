package com.ziyun.repairsystem.analysis.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ziyun.repairsystem.analysis.dao.RepairAnalysisMapper;
import com.ziyun.repairsystem.analysis.domain.*;
import com.ziyun.repairsystem.analysis.service.RepairAnalysisSrvice;
import com.ziyun.repairsystem.repair.dao.DistrictMapper;
import com.ziyun.repairsystem.repair.dao.RepairInfoMapper;
import com.ziyun.repairsystem.repair.domain.District;
import com.ziyun.repairsystem.repair.domain.RepairInfo;
import com.ziyun.repairsystem.repair.service.RepairInfoService;
import com.ziyun.repairsystem.taxhall.service.SwjgInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class RepairAnalysisServiceImpl implements RepairAnalysisSrvice{
    @Resource
    private RepairAnalysisMapper analysisMapper;

    @Resource
    private DistrictMapper districtMapper;

    @Override
    public Map<String,Object> queryAnalysisCount() {
        Map<String,Object> analysisCount = new HashMap<>();
        RepairAnalysisCount monthCount = analysisMapper.queryAnalysisByMonthNow();
        RepairAnalysisCount dayCount = analysisMapper.queryAnalysisByDay();
        RepairAnalysisCount newCount = analysisMapper.queryAnalysisByDayUnreviewed();
        analysisCount.put("monthCount",monthCount.getCount());
        analysisCount.put("dayCount",dayCount.getCount());
        analysisCount.put("newCount",newCount.getCount());
        return analysisCount;
    }

    @Override
    public Map<String,Object> queryAnalysisByType(String type) {
        Map<String,Object> analysisChart = new HashMap<>();
        List<String>  xAxis = new ArrayList<>();
        List<Integer>  count = new ArrayList<>();
        if(StringUtils.isNotBlank(type)&&StringUtils.equals(type,"year")){
            List<RepairAnalysisChart>  list = analysisMapper.queryAnalysisAllYear();
            for (RepairAnalysisChart chart : list) {
                xAxis.add(chart.getXAxis());
                count.add(chart.getCount());
            }
            analysisChart.put("xAxis",xAxis);
            analysisChart.put("count",count);
            return analysisChart;
        }else if(StringUtils.isNotBlank(type)&&StringUtils.equals(type,"month")) {
            List<RepairAnalysisChart>  list = analysisMapper.queryAnalysisByYear();
            for (RepairAnalysisChart chart : list) {
                xAxis.add(chart.getXAxis());
                count.add(chart.getCount());
            }
            analysisChart.put("xAxis",xAxis);
            analysisChart.put("count",count);
            return analysisChart;
        }else if(StringUtils.isNotBlank(type)&&StringUtils.equals(type,"day")){
            List<RepairAnalysisChart> list = analysisMapper.queryAnalysisByMonth();
            for (RepairAnalysisChart chart : list) {
                xAxis.add(chart.getXAxis());
                count.add(chart.getCount());
            }
            analysisChart.put("xAxis",xAxis);
            analysisChart.put("count",count);
            return analysisChart;
        }
        return null;
    }

    @Override
    public List<RepairAnalysisPie> queryAnalysisByDealType(String id) {
        Map<String,Object> analysisPie = new HashMap<>();
        List<RepairAnalysisPie> list = new ArrayList<>();
        if(StringUtils.isNotBlank(id)){
           list = analysisMapper.queryAnalysisByProcessDealType(Long.valueOf(id));
        }else {
            list = analysisMapper.queryAnalysisAllProcessDealType();
        }
        return list;
    }

    @Override
    public Map<String, Object> queryAnalysisBySection() {
        Map<String,Object> reMap = new HashMap<>();
        List<District> districtList = districtMapper.queryCityByProvince(1330000);
        List<String> yAxis = new ArrayList<>();
        List<Integer> unexamineList = new ArrayList<>();
        List<Integer> examinedList = new ArrayList<>();
        List<Integer> dealingList = new ArrayList<>();
        List<Integer> partDealedList = new ArrayList<>();
        List<Integer> dealedList = new ArrayList<>();

        for (District district : districtList) {
            yAxis.add(district.getDistrict());
            List<RepairAnalysisSection> sectionList = analysisMapper.queryAnalysisBySection(district.getDistrictId());
            for (RepairAnalysisSection section : sectionList) {
                switch (section.statusTag){
                    case 0:
                        unexamineList.add(section.value);
                        break;
                    case 1:
                        examinedList.add(section.value);
                        break;
                    case 2:
                        dealingList.add(section.value);
                        break;
                    case 3:
                        partDealedList.add(section.value);
                        break;
                    case 4:
                        dealedList.add(section.value);
                        break;
                    default:
                        break;
                }
            }
        }
        reMap.put("yAxis",yAxis);
        reMap.put("unexamine",unexamineList);
        reMap.put("examined",examinedList);
        reMap.put("dealing",dealingList);
        reMap.put("partDealed",partDealedList);
        reMap.put("dealed",dealedList);
        return reMap;
    }

    @Override
    public Map<String, Object> queryAnalysisByProcessor() {
        Map<String,Object> reMap = new HashMap<>();
        List<String>  xAxis = new ArrayList<>();
        List<Integer>  count = new ArrayList<>();
        List<RepairAnalysisChart> list = analysisMapper.queryAnalysisByProcessor();
        for (RepairAnalysisChart chart : list) {
            xAxis.add(chart.getXAxis());
            count.add(chart.getCount());
        }
        reMap.put("xAxis",xAxis);
        reMap.put("count",count);
        return reMap;
    }

}
