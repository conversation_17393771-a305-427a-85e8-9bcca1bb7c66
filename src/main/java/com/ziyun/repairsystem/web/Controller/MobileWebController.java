package com.ziyun.repairsystem.web.Controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.dysmsapi.model.v20170525.SendBatchSmsResponse;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.ziyun.repairsystem.common.domain.Area;
import com.ziyun.repairsystem.common.domain.QueryRequest;
import com.ziyun.repairsystem.common.domain.ResponseBo;
import com.ziyun.repairsystem.common.domain.SmsProperties;
import com.ziyun.repairsystem.common.service.SmsService;
import com.ziyun.repairsystem.common.utils.DateUtil;
import com.ziyun.repairsystem.repair.domain.*;
import com.ziyun.repairsystem.repair.service.DistrictService;
import com.ziyun.repairsystem.repair.service.MobileAreaService;
import com.ziyun.repairsystem.repair.service.RepairInfoService;
import com.ziyun.repairsystem.taxhall.domain.SwjgInfo;
import com.ziyun.repairsystem.taxhall.service.SwjgInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static com.ziyun.repairsystem.common.utils.DateUtil.FULL_TIME_PATTERN;

@Slf4j
@CrossOrigin
@RestController
public class MobileWebController {

//    public static Logger logger = LoggerFactory.getLogger(MobileWebController.class);

    @Autowired
    private MobileAreaService mobileAreaService;
    @Autowired
    private DistrictService districtService;
    @Autowired
    private SwjgInfoService swjgService;
    @Autowired
    private RepairInfoService repairInfoService;
    @Autowired
    private SmsService smsService;

    @Autowired
    private SmsProperties smsProperties;

    @GetMapping("web/getList")
    @ResponseBody
    public ResponseBo getDistrictList() {
        try {
            List<District> tree = districtService.findAllDistricts();
            return ResponseBo.ok(tree);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseBo.error("获取菜单列表失败！");
        }
    }


    @GetMapping("web/getCityJson")
    @ResponseBody
    public ResponseBo getDistrictTree() {
        try {
            List<Area> tree = districtService.getDistrictTree();
            return ResponseBo.ok(tree);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseBo.error("获取菜单列表失败！");
        }
    }

    @GetMapping("web/swjgAddress")
    @ResponseBody
    public ResponseBo getSwjgAddress(@RequestParam Map<String, String> params) throws IndexOutOfBoundsException {
        try {
            if (params.get("bsdtdjxh") != null) {
                SwjgInfo swjgInfos = swjgService.finByDJXH(params.get("bsdtdjxh"));
                if (StringUtils.isNotBlank(swjgInfos.getSwjgdz())) {
                    String address = swjgInfos.getSwjgdz();
                    log.info(address);
                    return ResponseBo.ok(address, "查询地址成功!");
                }
            }

            return ResponseBo.error("未查询到相关地址");
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseBo.error("获取菜单列表失败！");
        }
    }

    @PostMapping("web/areaBinding")
    @ResponseBody
    public ResponseBo areaBinding(@RequestBody Map<String, Object> params) {
        MobileArea mobileArea = new MobileArea();
        mobileArea.setMobile(params.get("mobile").toString());
        mobileArea.setAreaId(params.get("areaId").toString());
        mobileAreaService.areaBinding(mobileArea);
        return ResponseBo.ok("绑定成功!");
    }

    @PostMapping("web/areaUnBinding")
    @ResponseBody
    public ResponseBo areaUnBinding(@RequestBody Map<String, Object> params) {
        mobileAreaService.areaUnBinding(params.get("mobile").toString());
        return ResponseBo.ok("解绑成功!");
    }

    @PostMapping("web/existBinding")
    @ResponseBody
    public ResponseBo existBinding(@RequestBody Map<String, Object> params) {
        boolean b = mobileAreaService.existBinding(params.get("mobile").toString());
        if (b == true) {
            return ResponseBo.ok(true);
        }
        return ResponseBo.error(false);
    }

    @GetMapping(value = "web/searchRepair")
    @ResponseBody
    public Map<String, Object> searchRepair(HttpServletRequest rq) {
        Map<String, Object> reMap = new HashMap<>();
        RepairInfo repairInfo = new RepairInfo();
        if (StringUtils.isBlank(rq.getParameter("name")) && StringUtils.isBlank(rq.getParameter("mobile")) && StringUtils.isBlank(rq.getParameter("repairNumber"))) {
            return reMap;
        } else {
            if (StringUtils.isNotBlank(rq.getParameter("name"))) {
                repairInfo.setName(rq.getParameter("name"));
                log.info(repairInfo.getName());
            }
            if (StringUtils.isNotBlank(rq.getParameter("mobile"))) {
                repairInfo.setMobile(rq.getParameter("mobile"));
            }
            if (StringUtils.isNotBlank(rq.getParameter("repairNumber"))) {
                repairInfo.setRepairNumber(rq.getParameter("repairNumber"));
            }
            if (StringUtils.isNotBlank(rq.getParameter("addTimeFrom"))) {
                repairInfo.setAddTimeFrom(rq.getParameter("addTimeFrom"));
            }
            if (StringUtils.isNotBlank(rq.getParameter("addTimeTo"))) {
                repairInfo.setAddTimeTo(rq.getParameter("addTimeTo"));
            }
            List<RepairInfo> repairList = repairInfoService.findAllRepairInfoes(repairInfo);
            Collections.reverse(repairList);
            reMap.put("data", repairList);
        }
        return reMap;
    }


    @PostMapping("web/submitRepair")
    @ResponseBody
    public ResponseBo addRepairInfo(@RequestParam Map<String, String> reMap) {
        try {
            Map<String, String> batchMap = new HashMap<>();
            RepairInfo repairInfo = new RepairInfo();
            String webImg = reMap.get("img");
            if (StringUtils.isNotBlank(webImg)) {
                String[] arrImg = webImg.split(",");
                if (arrImg.length > 0) {
                    ArrayList<String> imgList = new ArrayList<>();
                    for (String img : arrImg) {
                        if (StringUtils.isNotBlank(img)) {
                            String url = "http://" + img;
                            log.debug("图片地址：" + url);
                            imgList.add(url);
                        }
                    }
                    repairInfo.setImg(imgList.toString());//
                }
            }
            SwjgInfo swjgInfo = swjgService.finByDJXH(reMap.get("swjgdm"));
            //通过前端获取的数据生成工单数据
            repairInfo.setName(reMap.get("name"));//报障人姓名
            repairInfo.setMobile(reMap.get("mobile").trim());//报障人联系方式
            repairInfo.setCont(reMap.get("cont"));//报障内容
            repairInfo.setTitle(reMap.get("title"));//报障类型
            repairInfo.setAddTime(new Date());
            repairInfo.setReceipt(0);
            if (reMap.get("address") != null) {
                if (StringUtils.equals(reMap.get("address"), swjgInfo.getSwjgdz()) && swjgInfo.getSwjgdz() != null) {
                    repairInfo.setAddress(swjgInfo.getSwjgdz());
                } else {
                    swjgInfo.setSwjgId(swjgInfo.getSwjgId());
                    swjgInfo.setSwjgdz(reMap.get("address"));
                    swjgService.updateSwjgInfo(swjgInfo);
                    repairInfo.setAddress(swjgInfo.getSwjgdz());
                }
            }
            //生成报障单号
            String repairNumber = DateUtil.getDateFormat(repairInfo.getAddTime(), FULL_TIME_PATTERN);
            repairInfo.setRepairNumber(repairNumber);//生成工单号
            repairInfo.setStateTag("0");
            //设置工单状态
            repairInfo.setBsdtmc(swjgInfo.getSwjgmc() + "-" + swjgInfo.getBsdtmc());
            repairInfo.setSwjgdm(swjgInfo.getBsdtdjxh());
            //添加工单
            repairInfoService.saveRepairInfo(repairInfo);

            //短信通知
            SendSmsResponse sendSmsResponse = smsService.sendSms(reMap.get("mobile"), smsProperties.getSmscode_1(), repairNumber);
            log.info(sendSmsResponse.toString());
            if (sendSmsResponse.getCode() != null && sendSmsResponse.getCode().equals("OK")) {
                batchMap.put("codeNumber", repairInfo.getRepairNumber());
                batchMap.put("name", repairInfo.getName());
                batchMap.put("phoneNumber", repairInfo.getMobile());
                SendBatchSmsResponse sendBatchSmsResponse = smsService.sendBatchSms(smsProperties.getSmscode_3(), batchMap);
                if (null != sendBatchSmsResponse.getCode() && sendBatchSmsResponse.getCode().equals("OK")) {
                    return ResponseBo.ok(repairNumber, "新增成功!");
                }
            }
//            return ResponseBo.ok(repairNumber, "新增成功!");
            return ResponseBo.error("短信通知失败，请联系网站管理员！");
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseBo.error("新增失败，请联系网站管理员！");
        }
    }
}
