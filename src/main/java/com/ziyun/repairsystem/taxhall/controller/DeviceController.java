package com.ziyun.repairsystem.taxhall.controller;

import java.util.*;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.ziyun.repairsystem.common.annotation.Log;
import com.ziyun.repairsystem.common.controller.BaseController;
import com.ziyun.repairsystem.common.domain.ResponseBo;
import com.ziyun.repairsystem.common.exception.ZiYunException;
import com.ziyun.repairsystem.taxhall.domain.Device;
import com.ziyun.repairsystem.taxhall.service.DeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("device")
public class DeviceController extends BaseController {
    String message;
    @Autowired
    DeviceService deviceService;

    @Log("新增设备")
    @PostMapping
//    @RequiresPermissions("deivce:add")
    public void addDevice(@Valid Device device) throws ZiYunException {
        try {
            this.deviceService.createDevice(device);
        } catch (Exception e) {
            message = "新增设备失败";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }

    @Log("删除设备")
    @DeleteMapping("/{deviceIds}")
//    @RequiresPermissions("deivce:delete")
    public void deleteDevices(@NotBlank(message = "{required}") @PathVariable String deviceIds) throws ZiYunException {
        try {
            String[] ids = deviceIds.split(StringPool.COMMA);
            this.deviceService.deleteDevices(ids);
        } catch (Exception e) {
            message = "删除设备失败";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }

    @Log("修改设备")
    @PutMapping
//    @RequiresPermissions("device:update")
    public ResponseBo updateDevices(HttpServletRequest request, HttpServletResponse response) throws ZiYunException {
        try {
            List<Device> deviceList = new ArrayList<Device> ();
            deviceList=JSONObject.parseArray(request.getParameter("devices"),Device.class);

            log.info(deviceList.toString());
//            log.info(devices);
//            ArrayList<Device> deviceList = new ArrayList<>();
//            Collections.addAll(deviceList,deviceArray);
            this.deviceService.updateBatchById(deviceList);
            return ResponseBo.ok("修改成功!");
        } catch (Exception e) {
            message = "修改设备失败";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }


    @GetMapping("/queryDeviceList")
    public ResponseBo querySwjgRo(@RequestParam(value = "taxId", required = false) String taxId) throws ZiYunException {
        try {
            List<Device> deviceList = deviceService.queryDeviceByTaxHall(taxId);
            return ResponseBo.ok(deviceList);

        } catch (Exception e) {
            message = "查询设备失败";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }
}
