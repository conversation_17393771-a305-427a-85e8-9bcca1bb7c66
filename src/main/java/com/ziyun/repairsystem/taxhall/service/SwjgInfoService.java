package com.ziyun.repairsystem.taxhall.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ziyun.repairsystem.common.domain.QueryRequest;
import com.ziyun.repairsystem.taxhall.domain.SwjgInfo;
import com.ziyun.repairsystem.taxhall.domain.SwjgRo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SwjgInfoService extends IService<SwjgInfo> {
    IPage<SwjgInfo> findSwjgInfoes(QueryRequest request, SwjgInfo swjgInfo);

    List<SwjgInfo> findSwjgInfoes(SwjgInfo swjgInfo);

    List<SwjgInfo> finAllSwjgInfoes();

    SwjgInfo findById(Long swjgId);

    SwjgInfo finByDJXH(String bsdtdjxh);

    List<String> selectBSDTDJXH(String districtId);

    List<SwjgInfo> queryBSDTDJXH(String districtId);

    void createSwjgInfo(SwjgInfo swjgInfo);

    void updateSwjgInfo(SwjgInfo swjgInfo);

    void deleteSwjgInfoes(String[] swjgInfoIds);

    SwjgRo querySwjgRo(String code);

    void updateSwjgRo(SwjgRo swjgRo);
}
