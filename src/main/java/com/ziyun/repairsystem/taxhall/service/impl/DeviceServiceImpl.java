package com.ziyun.repairsystem.taxhall.service.impl;

import com.ziyun.repairsystem.taxhall.domain.Device;
import com.ziyun.repairsystem.taxhall.dao.DeviceMapper;
import com.ziyun.repairsystem.taxhall.domain.SwjgInfo;
import com.ziyun.repairsystem.taxhall.service.DeviceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("deviceService")
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements DeviceService {

    /**
     * 通过传入的税务机关码查询设备信息
     *
     * @param id
     * @return
     */
    @Override
    public List<Device> queryDeviceByTaxHall(String id) {
        return this.baseMapper.queryDeviceByTaxHall(id);
    }

    /**
     * 添加设备
     * @param device
     */
    @Override
    public void createDevice(Device device) {
        log.info(device.toString());
        this.save(device);
    }

    /**
     * 更新设备信息
     * @param device
     */
    @Override
    @Transactional
    public void updateDevice(Device device) {
        this.updateById(device);
    }

    /**
     * 根据Id批量更新设备
     * @param deviceList
     */
    @Override
    @Transactional
    public void updateBatchIdsDevice(List<Device> deviceList) {
        log.info(deviceList.toString());
        this.updateBatchById(deviceList);
    }

    /**
     * 通过ids批量删除设备
     */
    @Override
    @Transactional
    public void deleteDevices(String[] deviceIds) {
        List<String> list = Arrays.asList(deviceIds);
        this.baseMapper.deleteBatchIds(list);
    }
}
