package com.ziyun.repairsystem.taxhall.service;

import com.ziyun.repairsystem.taxhall.domain.Device;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ziyun.repairsystem.taxhall.domain.SwjgInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DeviceService extends IService<Device> {

    /**
     * 通过传入的税务机关码查询设备信息
     * @param id
     * @return
     */
    List<Device> queryDeviceByTaxHall(String id);

    /**
     * 新增设备信息
     * @param device
     */
    void createDevice(Device device);

    /**
     * 修改设备信息
     * @param device
     */
    void updateDevice(Device device);

    /**
     * 删除设备信息
     * @param deviceIds
     */
    void deleteDevices(String[] deviceIds);

    /**
     * 批量更新
     * @param deviceList
     */
    void updateBatchIdsDevice(List<Device> deviceList);


}
