package com.ziyun.repairsystem.taxhall.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ziyun.repairsystem.common.domain.QueryRequest;
import com.ziyun.repairsystem.common.utils.SortUtil;
import com.ziyun.repairsystem.taxhall.dao.SwjgInfoMapper;
import com.ziyun.repairsystem.taxhall.domain.Device;
import com.ziyun.repairsystem.taxhall.domain.SwjgInfo;
import com.ziyun.repairsystem.taxhall.domain.SwjgRo;
import com.ziyun.repairsystem.taxhall.service.DeviceService;
import com.ziyun.repairsystem.taxhall.service.SwjgInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("swjgInfoService")
public class SwjgInfoServiceImpl extends ServiceImpl<SwjgInfoMapper, SwjgInfo> implements SwjgInfoService {

    @Autowired
    DeviceService deviceService;

    @Override
    public IPage<SwjgInfo> findSwjgInfoes(QueryRequest request, SwjgInfo swjgInfo) {
        try {
            LambdaQueryWrapper<SwjgInfo> queryWrapper = new LambdaQueryWrapper<>();
            if (StringUtils.isNotBlank(swjgInfo.getSwjgdm())) {
                queryWrapper.eq(SwjgInfo::getSwjgdm, swjgInfo.getSwjgdm());
            }
            if (StringUtils.isNotBlank(swjgInfo.getSwjgdsmc())) {
                queryWrapper.eq(SwjgInfo::getSwjgdsmc, swjgInfo.getSwjgdsmc());
            }
            if (StringUtils.isNotBlank(swjgInfo.getSwjgmc())) {
                queryWrapper.eq(SwjgInfo::getSwjgmc, swjgInfo.getSwjgmc());
            }
            if (StringUtils.isNotBlank(swjgInfo.getBsdtmc())) {
                queryWrapper.eq(SwjgInfo::getBsdtmc, swjgInfo.getBsdtmc());
            }
            if (StringUtils.isNotBlank(swjgInfo.getBsdtdjxh())) {
                queryWrapper.eq(SwjgInfo::getBsdtdjxh, swjgInfo.getBsdtdjxh());
            }
            Page<SwjgInfo> page = new Page<>();
            SortUtil.handlePageSort(request, page, true);
            return this.page(page, queryWrapper);
        } catch (Exception e) {
            log.error("获取大厅信息失败", e);
            return null;
        }
    }

    @Override
    public List<SwjgInfo> findSwjgInfoes(SwjgInfo swjgInfo) {
        try {
            LambdaQueryWrapper<SwjgInfo> queryWrapper = new LambdaQueryWrapper<>();
            if (StringUtils.isNotBlank(swjgInfo.getSwjgdm())) {
                queryWrapper.likeRight(SwjgInfo::getSwjgdm, swjgInfo.getSwjgdm());
            }
            if (StringUtils.isNotBlank(swjgInfo.getSwjgdsmc())) {
                queryWrapper.eq(SwjgInfo::getSwjgdsmc, swjgInfo.getSwjgdsmc());
            }
            if (StringUtils.isNotBlank(swjgInfo.getSwjgmc())) {
                queryWrapper.eq(SwjgInfo::getSwjgmc, swjgInfo.getSwjgmc());
            }
            if (StringUtils.isNotBlank(swjgInfo.getBsdtmc())) {
                queryWrapper.eq(SwjgInfo::getBsdtmc, swjgInfo.getBsdtmc());
            }
            if (StringUtils.isNotBlank(swjgInfo.getBsdtdjxh())) {
                queryWrapper.eq(SwjgInfo::getBsdtdjxh, swjgInfo.getBsdtdjxh());
            }
            if (StringUtils.isNotBlank(swjgInfo.getDistrictId())) {
                queryWrapper.eq(SwjgInfo::getDistrictId, swjgInfo.getDistrictId());
            }
            return this.baseMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("获取大厅信息失败", e);
            return null;
        }
    }

    @Override
    public SwjgInfo finByDJXH(String bsdtdjxh) {
        LambdaQueryWrapper<SwjgInfo> queryWrapper = new  LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(bsdtdjxh)) {
            queryWrapper.eq(SwjgInfo::getBsdtdjxh, bsdtdjxh);
        }
        return this.baseMapper.selectOne(queryWrapper);
    }

    @Override
    public List<String> selectBSDTDJXH(String districtId) {
        return this.baseMapper.selectBSDTDJXH(districtId+"%");
    }

    @Override
    public List<SwjgInfo> queryBSDTDJXH(String districtId) {
        return this.baseMapper.queryBSDTDJXH(districtId+"%");
    }

    /**
     * 查询所有
     * @return
     */
    @Override
    public List<SwjgInfo> finAllSwjgInfoes() {
        QueryWrapper<SwjgInfo> queryWrapper = new QueryWrapper<>();
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    public SwjgInfo findById(Long swjgId) {
        return this.baseMapper.selectById(swjgId);
    }

    @Override
    @Transactional
    public void createSwjgInfo(SwjgInfo swjgInfo) {
        this.save(swjgInfo);
    }

    @Override
    @Transactional
    public void updateSwjgInfo(SwjgInfo swjgInfo) {
        log.info(swjgInfo.toString());
        this.updateById(swjgInfo);
    }

    @Override
    @Transactional
    public void deleteSwjgInfoes(String[] swjgInfoIds) {
        List<String> list = Arrays.asList(swjgInfoIds);
        this.baseMapper.deleteBatchIds(list);
    }

    @Override
    public SwjgRo querySwjgRo(String code) {
        if(StringUtils.isNotBlank(code)){
            SwjgRo swjgRo = this.baseMapper.queryBSDTDJXHa(code);
            List<Device> deviceList =deviceService.queryDeviceByTaxHall(code);
            swjgRo.setDevices(deviceList);
            return swjgRo;
        }
        return null;
    }

    @Override
    public void updateSwjgRo(SwjgRo swjgRo) {

    }

//    @Override
//    @Transactional
//    public void updateSwjgRo(SwjgRo swjgRo) {
//        if(null!= swjgRo){
//            SwjgInfo swjgInfo = new SwjgInfo();
//            swjgInfo.setSwjgId(swjgRo.getSwjgId());
//            swjgInfo.setBsdtdjxh(swjgRo.getBsdtdjxh());
//            swjgInfo.setSwjgdm(swjgRo.getSwjgdm());
//            swjgInfo.setSwjgdsmc(swjgRo.getSwjgdsmc());
//            swjgInfo.setBsdtmc(swjgRo.getBsdtmc());
////            swj
//        }
//        if(null!=swjgRo.getDevices()){
//            List<Device> devices = swjgRo.getDevices();
//            deviceService.updateBatchById(devices);
//        }
//        this.updateById(swjgInfo);
//
//    }
}
