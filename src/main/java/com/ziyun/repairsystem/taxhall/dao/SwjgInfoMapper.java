package com.ziyun.repairsystem.taxhall.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ziyun.repairsystem.taxhall.domain.SwjgInfo;
import com.ziyun.repairsystem.taxhall.domain.SwjgRo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SwjgInfoMapper extends BaseMapper<SwjgInfo> {
    @Select("SELECT BSDTDJXH FROM t_swjg_info WHERE DISTRICT_ID like #{districtId}")
    List<String> selectBSDTDJXH(@Param("districtId") String districtId);
    @Select("SELECT * FROM t_swjg_info WHERE DISTRICT_ID like #{districtId}")
    List<SwjgInfo> queryBSDTDJXH(@Param("districtId") String districtId);

    @Select("SELECT * FROM t_swjg_info WHERE BSDTDJXH = #{bsdtdjxh}")
    SwjgRo queryBSDTDJXHa(@Param("bsdtdjxh")String bsdtdjxh);

    @Select("SELECT * FROM t_swjg_info tsi,t_device td WHERE tsi.BSDTDJXH = td.TAXHALL_ID and tsi.BSDTDJXH = #{code}")
    SwjgRo querySwjgRo(String code);
}
