package com.ziyun.repairsystem.taxhall.dao;

import com.ziyun.repairsystem.taxhall.domain.Device;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DeviceMapper extends BaseMapper<Device> {
    @Select("SELECT * FROM t_device WHERE TAXHALL_ID  = #{id}")
    List<Device> queryDeviceByTaxHall(String id);
}
