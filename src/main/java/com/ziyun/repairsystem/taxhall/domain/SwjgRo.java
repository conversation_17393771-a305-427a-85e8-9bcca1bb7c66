package com.ziyun.repairsystem.taxhall.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.wuwenze.poi.annotation.ExcelField;
import lombok.Data;

import java.util.List;

@Data
public class SwjgRo {

    @ExcelField(value = "税务机关ID")
    private Long swjgId;

    @ExcelField(value = "办税大厅登记序号")
    private String bsdtdjxh;

    @ExcelField(value = "税务机关代码")
    private String swjgdm;

    @ExcelField(value = "税务机关名称")
    private String swjgmc;

    @ExcelField(value = "办税大厅名称")
    private String bsdtmc;

    @ExcelField(value = "税务机关地市名称")
    private String swjgdsmc;

    @ExcelField(value = "税务机关地址")
    private String swjgdz;

    @TableField("DISTRICT_ID")
    private String districtId;

    @ExcelField(value = "联系人")
    private  String contactPerson;

    @ExcelField(value = "联系电话")
    private String tel;

    List<Device> devices;
}
