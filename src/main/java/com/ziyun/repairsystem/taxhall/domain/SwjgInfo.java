package com.ziyun.repairsystem.taxhall.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wuwenze.poi.annotation.Excel;
import com.wuwenze.poi.annotation.ExcelField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_swjg_info")
@Excel("办税大厅列表")
public class SwjgInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value="SWJG_ID", type = IdType.AUTO)
    @ExcelField(value = "税务机关ID")
    private Long swjgId;

    @TableField("BSDTDJXH")
    @ExcelField(value = "办税大厅登记序号")
    private String bsdtdjxh;

    @TableField("SWJGDM")
    @ExcelField(value = "税务机关代码")
    private String swjgdm;

    @TableField("SWJGMC")
    @ExcelField(value = "税务机关名称")
    private String swjgmc;

    @TableField("BSDTMC")
    @ExcelField(value = "办税大厅名称")
    private String bsdtmc;

    @TableField("SWJGDSMC")
    @ExcelField(value = "税务机关地市名称")
    private String swjgdsmc;

    @TableField("SWJGDZ")
    @ExcelField(value = "税务机关地址")
    private String swjgdz;

    @TableField("DISTRICT_ID")
    private String districtId;

    @TableField("CONTACT_PERSON")
    @ExcelField(value = "联系人")
    private  String contactPerson;

    @TableField("TEL")
    @ExcelField(value = "联系电话")
    private String tel;

}
