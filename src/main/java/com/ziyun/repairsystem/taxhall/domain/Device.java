package com.ziyun.repairsystem.taxhall.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_device")
public class Device implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value="ID",type = IdType.AUTO)
    private Long id;

    @TableField("DEVICE_NAME")
    private String deviceName;

    @TableField("DEVICE_DESCRIPTION")
    private String deviceDescription;

    @TableField("IP")
    private String ip;

    @TableField("TAXHALL_ID")
    private String taxhallId;
}
