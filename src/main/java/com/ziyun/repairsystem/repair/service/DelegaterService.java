package com.ziyun.repairsystem.repair.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ziyun.repairsystem.common.domain.QueryRequest;
import com.ziyun.repairsystem.repair.domain.Delegater;
import com.ziyun.repairsystem.repair.domain.RepairInfo;
import com.ziyun.repairsystem.repair.domain.bo.DelegaterBo;
import com.ziyun.repairsystem.repair.domain.vo.DelegaterVo;

import java.util.List;

public interface DelegaterService extends IService<Delegater> {

    void insertDelegater(DelegaterVo delegater);

    void updateDelegater(DelegaterVo delegater);


    List<DelegaterVo> findAllDelegateres();

    List<DelegaterVo> findAllDelegateresBySection(String section);

    DelegaterVo findDelegateresById(Integer id);

    void deleteDelegateres(String[] ids);
}
