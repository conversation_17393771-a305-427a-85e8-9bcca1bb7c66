package com.ziyun.repairsystem.repair.service.impl;

import com.ziyun.repairsystem.repair.domain.StaffInfo;
import com.ziyun.repairsystem.repair.dao.StaffInfoMapper;
import com.ziyun.repairsystem.repair.service.StaffInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class StaffInfoServiceImpl extends ServiceImpl<StaffInfoMapper, StaffInfo> implements StaffInfoService {

    @Override
    public List<StaffInfo> findAllStaffInfoes(StaffInfo staffInfo) {
        return null;
    }

    @Override
    public StaffInfo findById(Long staffId) {
        return null;
    }

    @Override
    public StaffInfo findByName(String name) {
        return null;
    }

    @Override
    public void addStaffInfo(StaffInfo staffInfo) {

    }

    @Override
    public void deleteStaffInfo(String staffInfoIds) {

    }

    @Override
    public void updateStaffInfo(StaffInfo staffInfo) {

    }
}
