package com.ziyun.repairsystem.repair.service.impl;

import java.util.ArrayList;
import java.util.Map;
import java.util.List;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ziyun.repairsystem.common.domain.SmsProperties;
import com.ziyun.repairsystem.common.service.SmsService;
import com.ziyun.repairsystem.common.utils.DateUtil;
import com.ziyun.repairsystem.repair.domain.*;
import com.ziyun.repairsystem.repair.dao.RepairInfoMapper;
import com.ziyun.repairsystem.repair.service.DistrictService;
import com.ziyun.repairsystem.repair.service.RepairInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ziyun.repairsystem.common.domain.QueryRequest;
import com.ziyun.repairsystem.common.utils.SortUtil;
import com.ziyun.repairsystem.system.service.UserService;
import com.ziyun.repairsystem.taxhall.service.SwjgInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.util.*;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class RepairInfoServiceImpl extends ServiceImpl<RepairInfoMapper, RepairInfo> implements RepairInfoService {
    @Autowired
    private UserService userService;

    @Autowired
    private SmsProperties smsProperties;

    @Autowired
    private SwjgInfoService swjgInfoService;

    @Autowired
    private SmsService smsService;

    @Autowired
    private DistrictService districtService;

    /**
     * 根据条件分页查询工单信息
     *
     * @param request
     * @param repairInfo
     * @return
     */
    @Override
    public IPage<RepairInfo> findRepairInfoes(QueryRequest request, RepairInfo repairInfo) {
        try {
            LambdaQueryWrapper<RepairInfo> queryWrapper = new LambdaQueryWrapper<>();
            if (null!=repairInfo.getRepairId()) {
                //工单号查询
                queryWrapper.eq(RepairInfo::getRepairId, repairInfo.getRepairId());
            }
            if (StringUtils.isNotBlank(repairInfo.getRepairNumber())) {
                //工单号查询
                queryWrapper.eq(RepairInfo::getRepairNumber, repairInfo.getRepairNumber());
            }
            if (StringUtils.isNotBlank(repairInfo.getSwjgdm())) {
                //税务机关
//                queryWrapper.in(RepairInfo::getSwjgdm, repairInfo.getSwjgdm());
                List<String> BSDTDJXH = swjgInfoService.selectBSDTDJXH(repairInfo.getSwjgdm());
                log.info(BSDTDJXH.toString());
                queryWrapper.in(RepairInfo::getSwjgdm, BSDTDJXH);
            }
            if (StringUtils.isNotBlank(repairInfo.getTitle())) {
                //报障类型查询
                queryWrapper.eq(RepairInfo::getTitle, repairInfo.getTitle());
            }
            if (StringUtils.isNotBlank(repairInfo.getMobile())) {
                //报障手机查询
                queryWrapper.eq(RepairInfo::getMobile, repairInfo.getMobile());
            }
            if (StringUtils.isNotBlank(repairInfo.getStateTag())) {
                //维护人查询
                queryWrapper.eq(RepairInfo::getStateTag, repairInfo.getStateTag());
            }
            if (null!=repairInfo.getUserId()) {
                //维护人查询
                queryWrapper.eq(RepairInfo::getUserId, repairInfo.getUserId());
            }
//            if (StringUtils.isNotBlank(repairInfo.getRealName())) {
//                //维护人查询
//                queryWrapper.eq(RepairInfo::getRealName, repairInfo.getRealName());
//            }
//            if(StringUtils.isNotBlank(repairInfo.getDealType())){
//                queryWrapper.eq(RepairInfo::getDealType,repairInfo.getDealType());
//            }
            if (StringUtils.isNotEmpty(repairInfo.getAddTimeFrom()) && StringUtils.isNotEmpty(repairInfo.getAddTimeTo())) {
                //报障时段查询
                queryWrapper
                        .ge(RepairInfo::getAddTime, repairInfo.getAddTimeFrom())
                        .le(RepairInfo::getAddTime, repairInfo.getAddTimeTo());
            }
            queryWrapper.orderByDesc(RepairInfo::getRepairId);
            Page<RepairInfo> page = new Page<>();
            SortUtil.handlePageSort(request, page, true);
            return this.page(page, queryWrapper);
        } catch (Exception e) {
            log.error("获取工单信息失败", e);
            return null;
        }
    }

    /**
     * 根据name,repairNumber,addTime,mobile返回工单不分页,手机端方法
     *
     * @param repairInfo
     * @return
     */
    @Override
    public List<RepairInfo> findAllRepairInfoes(RepairInfo repairInfo) {
        try {
            LambdaQueryWrapper<RepairInfo> queryWrapper = new LambdaQueryWrapper<>();
            if (StringUtils.isNotBlank(repairInfo.getName())) {
                //姓名查询
                queryWrapper.eq(RepairInfo::getName, repairInfo.getName());
            }
            if (StringUtils.isNotBlank(repairInfo.getRepairNumber())) {
                //工单号查询
                queryWrapper.eq(RepairInfo::getRepairNumber, repairInfo.getRepairNumber());
            }
            if (StringUtils.isNotBlank(repairInfo.getStateTag())) {
                //状态查询
                queryWrapper.eq(RepairInfo::getStateTag, repairInfo.getStateTag());
            }
            if (StringUtils.isNotBlank(repairInfo.getMobile())) {
                //报障手机查询
                queryWrapper.eq(RepairInfo::getMobile, repairInfo.getMobile());
            }
            if (StringUtils.isNotBlank(repairInfo.getAddTimeFrom()) && StringUtils.isNotBlank(repairInfo.getAddTimeTo())) {
                //报障时段查询
                queryWrapper
                        .ge(RepairInfo::getAddTime, repairInfo.getAddTimeFrom())
                        .le(RepairInfo::getAddTime, repairInfo.getAddTimeTo());
            }
            return this.baseMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("获取工单信息失败", e);
            return new ArrayList<RepairInfo>();
        }
    }

    /**
     * 搜索所有工单不分页
     *
     * @return
     */
    @Override
    public List<RepairInfo> findAllRepairInfoes() {
        LambdaQueryWrapper<RepairInfo> queryWrapper = new LambdaQueryWrapper<>();
        return this.baseMapper.selectList(queryWrapper);
    }

    /**
     * 根据类型、时间等条件统计工单数量
     *
     * @param repairInfo
     * @return
     */
    @Override
    public int repairStatistics(RepairInfo repairInfo) {
        QueryWrapper<RepairInfo> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(repairInfo.getTitle())) {
            //报障类型
            queryWrapper.lambda().eq(RepairInfo::getTitle, repairInfo.getTitle());
        }
        if (StringUtils.isNotBlank(repairInfo.getSwjgdm())) {
            //税务机关代码
            queryWrapper.lambda().eq(RepairInfo::getSwjgdm, repairInfo.getSwjgdm());
        }
        if (StringUtils.isNotBlank(repairInfo.getAddTimeFrom()) && StringUtils.isNotBlank(repairInfo.getAddTimeTo())) {
            //报障时段查询
            queryWrapper.lambda()
                    .ge(RepairInfo::getAddTime, repairInfo.getAddTimeFrom())
                    .le(RepairInfo::getAddTime, repairInfo.getAddTimeTo());
        }
        return this.baseMapper.selectCount(queryWrapper);
    }

    /**
     * @param cityCode
     * @return
     */
    @Override
    public Map<String, Object> analysisByCityCode(String cityCode) {
        Map<String, Object> anaMap = new HashMap<>();
        DecimalFormat df = new DecimalFormat("##.##%");

        //当日报障统计
        RepairInfo repair = new RepairInfo();
        repair.setAddTimeFrom(DateUtil.getStartTimeFormat(DateUtil.FULL_TIME_SPLIT_PATTERN));
        repair.setAddTimeTo(DateUtil.getEndTimeFormat(DateUtil.FULL_TIME_SPLIT_PATTERN));
        List<RepairInfo> relist = findAllRepairInfoes(repair);

//        log.info("relist" + relist.size());
        //所有完成
        RepairInfo repairFinished = new RepairInfo();
        repairFinished.setStateTag("4");
        List<RepairInfo> finishedList = findAllRepairInfoes(repairFinished);
        log.info("finishedList" + finishedList.size());

        if (StringUtils.isNotBlank(cityCode) && cityCode.equals("1330000")) {
            int contAnalysis = findAllRepairInfoes().size();
            log.info("所有: " + contAnalysis);

            int todayAnalysis = relist.size();
            anaMap.put("today", todayAnalysis);

            double finishedRate = finishedList.size() / (double) contAnalysis;
            anaMap.put("rate", df.format(finishedRate));
            anaMap.put("all", contAnalysis);
        } else {
            int todayAnalysis = relist.size();
            anaMap.put("today", todayAnalysis);

            int contAnalysis = statisticsByCity(Integer.valueOf(cityCode));
            double finishedRate = finishedList.size() / (double) contAnalysis;
            anaMap.put("rate", df.format(finishedRate));
            anaMap.put("all", contAnalysis);
        }
        return anaMap;
    }

    /**
     * 通过id查询工单信息
     *
     * @param id
     * @return
     */
    @Override
    public RepairInfo findById(Long id) {
        return this.baseMapper.selectById(id);
    }

    @Override
    public RepairInfo findByName(String name) {
        return null;
    }

    /**
     * 添加
     * @param repairInfo
     */
    @Override
    @Transactional
    public void saveRepairInfo(RepairInfo repairInfo) {
        this.save(repairInfo);
    }

    /**
     * 根据id更新工单信息
     * @param repairInfo
     */
    @Override
    @Transactional
    public void updateRepairInfo(RepairInfo repairInfo) {
        this.updateById(repairInfo);
    }

    /**
     * 批量删除工单
     * @param repairInfoIds
     */
    @Override
    @Transactional
    public void deleteRepairInfoes(String[] repairInfoIds) {
        List<String> list = Arrays.asList(repairInfoIds);
        this.baseMapper.deleteBatchIds(list);
    }


    @Override
    public List<Map<String, Object>> repairTitleStatisticsByCity(String code) {
        log.info(code + "-----");
        List<District> districtList = districtService.findByPid(code);
        Map<String, Object> titleMap = new HashMap<>();
        List<Map<String, Object>> reList = new ArrayList<>();
        for (District district : districtList) {
            titleMap = new HashMap<>();
            titleMap.put("name", district.getDistrict());
            titleMap.put("value", statisticsByCity(district.getDistrictId()));
            log.info(statisticsByCity(district.getDistrictId()) + " -------------");
            reList.add(titleMap);
        }
        return reList;
    }


    /**
     * 根据传入月份的数量统计
     *
     * @return
     */
    @Override
    public Map<String, Object> analysisByMonth(Integer month) {
        Map<String, Object> reMap = new HashMap<>();
        RepairInfo repair = new RepairInfo();
        Date nowDate = new Date();
        List<String> monthList = new ArrayList<>();
        List<String> valueList = new ArrayList<>();
        for (int i = 0; i < month; i++) {
            Map<String, String> dateMap = DateUtil.getFirstday_Lastday_Month(i);
            repair.setAddTimeFrom(dateMap.get("first"));
            repair.setAddTimeTo(dateMap.get("last"));
            int total = repairStatistics(repair);
            monthList.add(dateMap.get("month") + "月");
            valueList.add(String.valueOf(total));
        }
        reMap.put("month", monthList);
        reMap.put("value", valueList);
        return reMap;
    }

    public int statisticsByCity(Integer code) {
        return this.baseMapper.repairTitleStatisticsByCity(code);
    }

}




