package com.ziyun.repairsystem.repair.service;

import com.ziyun.repairsystem.repair.domain.MobileArea;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface MobileAreaService extends IService<MobileArea> {
    List<MobileArea> findMobileArea(String mobile);

    void areaBinding(MobileArea mobileArea);

    void areaUnBinding(String mobiles);

    boolean existBinding(String mobile);

    Map<String,Object> findDistrictByMobile(String mobile);

    MobileArea findByOne(String mobile);
}
