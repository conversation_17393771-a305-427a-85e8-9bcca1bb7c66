package com.ziyun.repairsystem.repair.service;

import com.ziyun.repairsystem.common.domain.QueryRequest;
import com.ziyun.repairsystem.repair.domain.District;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ziyun.repairsystem.common.domain.Area;
import com.ziyun.repairsystem.system.domain.Dept;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface DistrictService extends IService<District> {
    Map<String, Object> findDistricts(QueryRequest request, District district);

    List<District> findDistricts(District district, QueryRequest request);

    List<District> findAllDistricts();

    List<District> findByLevel(String level);

    List<District> findByPid(String pid);

    List<Area> getDistrictTree();

    District findById(String id);

    District findBySWJGDM(String swjgdm);

    String concatRegion(String swjgdm);

    void createDistrict(District district);

    void updateDistrict(District district);

    void deleteDistricts(String[] districtIds);
}
