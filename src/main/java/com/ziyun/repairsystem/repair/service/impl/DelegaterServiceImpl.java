package com.ziyun.repairsystem.repair.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ziyun.repairsystem.repair.dao.DelegaterMapper;
import com.ziyun.repairsystem.repair.domain.Delegater;
import com.ziyun.repairsystem.repair.domain.DelegaterDistrict;
import com.ziyun.repairsystem.repair.domain.vo.DelegaterDistrictVo;
import com.ziyun.repairsystem.repair.domain.bo.DelegaterBo;
import com.ziyun.repairsystem.repair.domain.vo.DelegaterVo;
import com.ziyun.repairsystem.repair.service.DelegaterDistrictService;
import com.ziyun.repairsystem.repair.service.DelegaterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
@Service("delegaterService")
public class DelegaterServiceImpl extends ServiceImpl<DelegaterMapper, Delegater> implements DelegaterService {

    @Resource
    private DelegaterDistrictService delegaterDistrictService;

    @Override
    public void insertDelegater(DelegaterVo vo) {
        // 插入 DelegaterVo 对象
        Delegater delegater = new Delegater(vo.id, vo.name, vo.companyName, vo.payAccount,vo.section, vo.phoneNumber);
        int bl = this.baseMapper.insert(delegater);
        if(bl!=0){
            int id = delegater.getId();
            for (DelegaterDistrictVo districtVo : vo.getUnitList()) {
                DelegaterDistrict delegaterDistrict = new DelegaterDistrict(id,districtVo.getValue());
                this.baseMapper.insertDelegaterDistrict(delegaterDistrict);
            }
        }
    }

    @Override
    public void updateDelegater(DelegaterVo delegater) {
        // 更新 DelegaterVo 对象
        this.baseMapper.updateDelegater(delegater);

        // 删除与 DelegaterVo 对象关联的所有 DelegaterDistrict 对象
        this.baseMapper.deleteDelegaterDistricts(delegater.getId());

        // 遍历 DelegaterDistrictVo 对象列表，插入每个对象
        for (DelegaterDistrictVo districtVo : delegater.getUnitList()) {
            DelegaterDistrict delegaterDistrict = new DelegaterDistrict(delegater.getId(),districtVo.getValue());
            this.baseMapper.insertDelegaterDistrict(delegaterDistrict);
        }
    }



    @Override
    public List<DelegaterVo> findAllDelegateres() {
        return this.baseMapper.selectAllDelegater();
    }

    @Override
    public List<DelegaterVo> findAllDelegateresBySection(String section) {
        return this.baseMapper.selectAllDelegaterBySection(section);
    }

    @Override
    public DelegaterVo findDelegateresById(Integer id) {
        return this.baseMapper.selectDelegaterById(id);
    }

    @Override
    public void deleteDelegateres(String[] ids) {
        List<String> list = Arrays.asList(ids);
        this.baseMapper.deleteBatchIds(list);
        // 删除与 Delegater 对象关联的所有 DelegaterDistrict 对象
        for (String id: list) {
            this.baseMapper.deleteDelegaterDistricts(Integer.valueOf(id));
        }
    }


    private LambdaQueryWrapper<Delegater> buildQueryWrapper(DelegaterBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Delegater> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getDelegaterName()), Delegater::getDelegaterName, bo.getDelegaterName());
        lqw.like(StringUtils.isNotBlank(bo.getCompanyName()), Delegater::getCompanyName, bo.getCompanyName());
        lqw.like(StringUtils.isNotBlank(bo.getPayAccount()), Delegater::getPayAccount, bo.getPayAccount());
        lqw.like(StringUtils.isNotBlank(bo.getResponsibleArea()), Delegater::getSection, bo.getResponsibleArea());
        return lqw;
    }
}
