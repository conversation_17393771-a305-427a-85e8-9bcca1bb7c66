package com.ziyun.repairsystem.repair.service;

import com.ziyun.repairsystem.repair.domain.Delegater;
import com.ziyun.repairsystem.repair.domain.DelegaterDistrict;

import java.util.List;

public interface DelegaterDistrictService {
    List<Integer> selectDistrictListByDelegaterId(Integer delegaterId);

    boolean saveOrUpdate(List<DelegaterDistrict> entityList);

    void delete(String[] delegaterIds);

    List<DelegaterDistrict> findById(DelegaterDistrict delegaterDistrict);
}
