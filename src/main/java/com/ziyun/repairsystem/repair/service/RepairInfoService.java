package com.ziyun.repairsystem.repair.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ziyun.repairsystem.repair.domain.RepairInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ziyun.repairsystem.common.domain.QueryRequest;
import org.apache.ibatis.annotations.Select;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface RepairInfoService extends IService<RepairInfo> {

    IPage<RepairInfo> findRepairInfoes(QueryRequest request, RepairInfo repairInfo);

    List<RepairInfo> findAllRepairInfoes(RepairInfo repairInfo);

    List<RepairInfo> findAllRepairInfoes();

    RepairInfo findById(Long id);

    RepairInfo findByName(String name);

    int repairStatistics(RepairInfo repairInfo);

    void saveRepairInfo(RepairInfo repairInfo);

    void updateRepairInfo(RepairInfo repairInfo);

    void deleteRepairInfoes(String[] repairInfoIds);

    Map<String,Object> analysisByCityCode(String cityCode);



    List<Map<String,Object>> repairTitleStatisticsByCity(String code);

    Map<String,Object> analysisByMonth(Integer month);
}
