package com.ziyun.repairsystem.repair.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ziyun.repairsystem.common.domain.QueryRequest;
import com.ziyun.repairsystem.common.domain.Tree;
import com.ziyun.repairsystem.common.domain.ZiYunConstant;
import com.ziyun.repairsystem.common.utils.AreaUtils;
import com.ziyun.repairsystem.common.utils.SortUtil;
import com.ziyun.repairsystem.common.utils.TreeUtil;
import com.ziyun.repairsystem.repair.domain.District;
import com.ziyun.repairsystem.repair.dao.DistrictMapper;
import com.ziyun.repairsystem.repair.domain.RepairInfo;
import com.ziyun.repairsystem.repair.service.DistrictService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ziyun.repairsystem.common.domain.Area;
import com.ziyun.repairsystem.system.domain.Dept;
import com.ziyun.repairsystem.taxhall.service.SwjgInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("districtService")
public class DistrictServiceImpl extends ServiceImpl<DistrictMapper, District> implements DistrictService {

    @Autowired
    private SwjgInfoService swjgInfoService;


    @Override
    public Map<String, Object> findDistricts(QueryRequest request, District district) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<District> districts = findAllDistricts();
            List<Area> districtTree = getDistrictTree();
            result.put("rows", districtTree);
            result.put("total", districts.size());
        } catch (Exception e) {
            log.error("获取地区列表失败", e);
            result.put("rows", null);
            result.put("total", 0);
        }
        return result;
    }

    /**
     * 分页查询District数据
     *
     * @param district
     * @param request
     * @return
     */
    @Override
    public List<District> findDistricts(District district, QueryRequest request) {
        QueryWrapper<District> queryWrapper = new QueryWrapper<>();
        SortUtil.handleWrapperSort(request, queryWrapper, "orderNum", ZiYunConstant.ORDER_ASC, true);
        return this.baseMapper.selectList(queryWrapper);
    }

    /**
     * 查询所有District数据
     */
    @Override
    public List<District> findAllDistricts() {
        QueryWrapper<District> queryWrapper = new QueryWrapper<>();
        return this.baseMapper.selectList(queryWrapper);
    }

    /**
     * 通过level字段查询District
     *
     * @param level
     * @return
     */
    @Override
    public List<District> findByLevel(String level) {
        LambdaQueryWrapper<District> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(level)) {
            //根据级别查询
            queryWrapper.eq(District::getLevel, level);
        }
        return this.baseMapper.selectList(queryWrapper);
    }

    /**
     * 通过pid属性查询District列表
     *
     * @param pid
     * @return
     */
    @Override
    public List<District> findByPid(String pid) {
        LambdaQueryWrapper<District> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(pid)) {
            //根据级别查询
            queryWrapper.eq(District::getPid, pid);
            return this.baseMapper.selectList(queryWrapper);
        }
        return null;
    }

    /**
     * 创建District信息
     *
     * @param district
     */
    @Override
    @Transactional
    public void createDistrict(District district) {
        Integer parentId = district.getPid();
        if (parentId == null)
            district.setPid(0);
        this.save(district);
    }

    /**
     * 更新District信息
     *
     * @param district
     */
    @Override
    @Transactional
    public void updateDistrict(District district) {
        this.baseMapper.updateById(district);
    }


    /**
     * 通过ids批量删除District
     *
     * @param districtIds
     */
    @Override
    @Transactional
    public void deleteDistricts(String[] districtIds) {
        List<String> list = Arrays.asList(districtIds);
        this.baseMapper.deleteBatchIds(list);
    }

    /**
     * 获取DistrictTree
     *
     * @return
     */
    @Override
    public List<Area> getDistrictTree() {
        List<Area<District>> trees = new ArrayList<Area<District>>();
        List<District> districts = this.findAllDistricts();
        List<Area> tList = buildTree(districts, "1");
        return tList;
    }

    /**
     * 通过id查询District
     *
     * @param id
     * @return
     */
    @Override
    public District findById(String id) {
        District district = this.baseMapper.selectById(id);
        return district;
    }

    @Override
    public District findBySWJGDM(String swjgdm) {
        return this.baseMapper.findByBSDTDJXH(swjgdm);
    }

    @Override
    public String concatRegion(String swjgdm) {
        String region = null;
        District district = findBySWJGDM(swjgdm);
        if(district.getLevel()>=3){
            District district1 = findById(district.getPid().toString());
            region = district1.getDistrict();
        }
        region = region+"-"+district.getDistrict();
        return region;
    }

    /**
     * 构建树结构
     */
    private List<Area> buildTree(List<District> nodes, String id) {
        List<Area> topNode = new ArrayList<>();
        List<Area> secNode = new ArrayList<>();
        List<Area> thrNode = new ArrayList<>();
        if (nodes == null) {
            return null;
        }
        for (District district1 : nodes) {
            Integer pid1 = Integer.valueOf(id);
            if (district1.getPid().equals(pid1)) {
                Area area1 = new Area();
                area1.setValue(district1.getDistrictId().toString());
                area1.setLabel(district1.getDistrict());
                Integer pid2 = district1.getDistrictId();
                for (District district2 : nodes) {
                    if (district2.getPid().equals(pid2)) {
                        Area area2 = new Area();
                        area2.setValue(district2.getDistrictId().toString());
                        area2.setLabel(district2.getDistrict());
                        Integer pid3 = district2.getDistrictId();
                        for (District district3 : nodes) {
                            if (district3.getPid().equals(pid3)) {
                                Area area3 = new Area();
                                area3.setValue(district3.getDistrictId().toString());
                                area3.setLabel(district3.getDistrict());
                                thrNode.add(area3);
                            }
                        }
                        area2.setChildren(thrNode);
                        secNode.add(area2);
                        thrNode = new ArrayList<>();
                    }
                }
                area1.setChildren(secNode);
                topNode.add(area1);
                secNode = new ArrayList<>();
            }
        }
        return topNode;
    }
}
