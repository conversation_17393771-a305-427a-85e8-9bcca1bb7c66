package com.ziyun.repairsystem.repair.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ziyun.repairsystem.repair.dao.DelegaterDistrictMapper;
import com.ziyun.repairsystem.repair.domain.DelegaterDistrict;
import com.ziyun.repairsystem.repair.service.DelegaterDistrictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;


@Slf4j
@Service
public class DelegaterDistrictServiceImpl extends ServiceImpl<DelegaterDistrictMapper, DelegaterDistrict> implements DelegaterDistrictService {

    @Override
    public List<Integer> selectDistrictListByDelegaterId(Integer delegaterId) {
        return baseMapper.selectDistrictListByDelegaterId(delegaterId);
    }

    @Override
    public boolean saveOrUpdate(List<DelegaterDistrict> entityList) {
        return super.saveOrUpdateBatch(entityList);
    }

    @Override
    public void delete(String[] delegaterIds) {
        List<String> list = Arrays.asList(delegaterIds);
        this.baseMapper.deleteBatchIds(list);
    }

    @Override
    public List<DelegaterDistrict> findById(DelegaterDistrict delegaterDistrict) {
        LambdaQueryWrapper<DelegaterDistrict> queryWrapper = new LambdaQueryWrapper<>();
        if (delegaterDistrict.getDelegaterId()!=null) {
            //通过Id查询
            queryWrapper.eq(DelegaterDistrict::getDelegaterId, delegaterDistrict.getDelegaterId());
        }
       return this.baseMapper.selectList(queryWrapper);
    }
}
