package com.ziyun.repairsystem.repair.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ziyun.repairsystem.common.domain.QueryRequest;
import com.ziyun.repairsystem.common.domain.ZiYunConstant;
import com.ziyun.repairsystem.common.utils.SortUtil;
import com.ziyun.repairsystem.repair.dao.DistrictMapper;
import com.ziyun.repairsystem.repair.domain.District;
import com.ziyun.repairsystem.repair.domain.MobileArea;
import com.ziyun.repairsystem.repair.dao.MobileAreaMapper;
import com.ziyun.repairsystem.repair.service.DistrictService;
import com.ziyun.repairsystem.repair.service.MobileAreaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ziyun.repairsystem.system.domain.Dept;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class MobileAreaServiceImpl extends ServiceImpl<MobileAreaMapper, MobileArea> implements MobileAreaService {

    @Autowired
    private DistrictMapper districtMapper;

    @Override
    public List<MobileArea> findMobileArea(String mobile) {
        QueryWrapper<MobileArea> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(mobile))
            queryWrapper.lambda().eq(MobileArea::getMobile, mobile);
        return this.baseMapper.selectList(queryWrapper);
    }

    @Override
    //绑定手机与区域
    public void areaBinding(MobileArea mobileArea) {
        this.save(mobileArea);
    }

    @Override
    public void areaUnBinding(String mobiles) {
        List<String> list = Arrays.asList(mobiles.split(","));
        this.baseMapper.deleteBatchIds(list);
    }

    @Override
    public boolean existBinding(String mobile) {
        List<MobileArea> list = findMobileArea(mobile);
        if (list.size()>0){
            return true;
        }
        return false;
    }

    @Override
    public Map<String, Object> findDistrictByMobile(String mobile) {
        District fDistrict = this.districtMapper.selectById(findByOne(mobile).getAreaId());
        List<District> districtList = districtMapper.findByMobile(mobile);
        Map<String, Object> fMap = new HashMap<>();
        fMap.put("name",fDistrict.getDistrict());
        fMap.put("value",fDistrict.getDistrictId());
        Map<String, Object> cityMap = new HashMap<>();
        List<Map<String, Object>> cityList = new ArrayList<>();
        for (District district :districtList){
            cityMap = new HashMap<>();
            cityMap.put("name", district.getDistrict());
            cityMap.put("value", district.getDistrictId());
            cityList.add(cityMap);
        }
        fMap.put("children",cityList);

        return fMap;
    }

    @Override
    public MobileArea findByOne(String mobile) {
        QueryWrapper<MobileArea> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(mobile))
            queryWrapper.lambda().eq(MobileArea::getMobile, mobile);
        return this.baseMapper.selectOne(queryWrapper);
    }
}
