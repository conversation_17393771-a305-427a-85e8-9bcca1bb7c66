package com.ziyun.repairsystem.repair.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ziyun.repairsystem.repair.domain.Delegater;
import com.ziyun.repairsystem.repair.domain.DelegaterDistrict;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DelegaterDistrictMapper extends BaseMapper<DelegaterDistrict> {

    List<Integer> selectDistrictListByDelegaterId(@Param("delegaterId") Integer delegaterId);

}
