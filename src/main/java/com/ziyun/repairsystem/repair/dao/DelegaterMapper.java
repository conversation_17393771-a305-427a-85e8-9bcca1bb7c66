package com.ziyun.repairsystem.repair.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ziyun.repairsystem.repair.domain.Delegater;
import com.ziyun.repairsystem.repair.domain.DelegaterDistrict;
import com.ziyun.repairsystem.repair.domain.vo.DelegaterDistrictVo;
import com.ziyun.repairsystem.repair.domain.vo.DelegaterVo;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface DelegaterMapper extends BaseMapper<Delegater> {

    DelegaterVo selectDelegaterById(Integer id);

    void insertDelegater(DelegaterVo delegater);

    void updateDelegater(DelegaterVo delegater);

    void deleteDelegater(Integer delegaterId);

    List<DelegaterVo> selectAllDelegater();

    @Select("SELECT d.id,d.company_name,d.pay_account,d.phone_number,d.delegater_name,d.section,td.BSDTMC,td.BSDTDJXH\n" +
            "        FROM t_delegater d\n" +
            "                 LEFT JOIN t_delegater_district dd ON d.id = dd.delegater_id\n" +
            "                 LEFT JOIN t_swjg_info td ON dd.bsdtdjxh = td.BSDTDJXH\n" +
            "        where section = #{section}")
    List<DelegaterVo> selectAllDelegaterBySection(String section);

    void insertDelegaterDistrict(DelegaterDistrict delegater);

    void deleteDelegaterDistricts(Integer delegaterId);

    Delegater selectDelegater(DelegaterVo vo);

}
