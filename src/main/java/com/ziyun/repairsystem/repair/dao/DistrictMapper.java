package com.ziyun.repairsystem.repair.dao;

import com.ziyun.repairsystem.repair.domain.District;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface DistrictMapper extends BaseMapper<District> {
    @Select("SELECT * FROM t_district td,t_mobile_area tma WHERE tma.AREA_ID = td.pid AND tma.MOBILE=#{mobile}")
    List<District> findByMobile(String mobile);

    @Select("SELECT d2.district_id,d2.district\n" +
            "FROM t_district d1\n" +
            "JOIN t_district d2 ON d1.district_id = d2.pid\n" +
            "WHERE d1.district_id = #{id}")
    List<District> queryCityByProvince(Integer id);

    /**
     * BSDTDJXH 就相当于 repairInfo中的swjgdm
     * @param id
     * @return
     */
    @Select("SELECT t2.district_id,t2.pid,t2.district,t2.level\n" +
            "FROM t_swjg_info t1\n" +
            "         JOIN t_district t2 ON t1.district_id = t2.district_id\n" +
            "WHERE t1.BSDTDJXH = #{id}")
    District findByBSDTDJXH(String id);
}
