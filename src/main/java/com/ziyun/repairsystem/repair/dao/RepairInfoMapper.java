package com.ziyun.repairsystem.repair.dao;

import com.ziyun.repairsystem.repair.domain.RepairInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 */
public interface RepairInfoMapper extends BaseMapper<RepairInfo> {
    @Select("SELECT COUNT(*) FROM t_repair_info tri,t_swjg_info tsi,t_district td WHERE tsi.BSDTDJXH = tri.SWJGDM AND tsi.DISTRICT_ID = td.district_id AND td.pid = #{code}")
    int repairTitleStatisticsByCity(@Param("code")Integer code);


//    int
}
