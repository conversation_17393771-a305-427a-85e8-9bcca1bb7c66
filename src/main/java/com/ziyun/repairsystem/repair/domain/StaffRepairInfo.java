package com.ziyun.repairsystem.repair.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class StaffRepairInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableField( "ID")
    private Long id;

    @TableField( "REPAIR_ID")
    private Long repairId;

    @TableField( "STAFF_ID")
    private Long staffId;

    @TableField( "ADD_TIME")
    private Date addTime;

    @TableField("ADD_USER")
    private String addUser;
}