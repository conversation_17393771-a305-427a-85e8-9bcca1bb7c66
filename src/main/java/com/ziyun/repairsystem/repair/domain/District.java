package com.ziyun.repairsystem.repair.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wuwenze.poi.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 地区表
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_district")
@Excel("区域表")
public class District implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "district_id")
    private Integer districtId;

    /**
     * 父及关系
     */
    @TableId(value = "pid")
    private Integer pid;

    /**
     * 地区名称
     */
    @TableId(value = "district")
    private String district;

    /**
     * 子属关系
     */
    @TableId(value = "level")
    private Integer level;


}
