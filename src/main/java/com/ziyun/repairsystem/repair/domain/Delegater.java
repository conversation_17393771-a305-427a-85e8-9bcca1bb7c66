package com.ziyun.repairsystem.repair.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wuwenze.poi.annotation.Excel;
import com.ziyun.repairsystem.common.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("t_delegater")
@Excel("运维人员表")
@AllArgsConstructor
@NoArgsConstructor
public class Delegater extends BaseEntity {


    @TableId(type = IdType.AUTO)
    private Integer id;
    @TableField(value = "delegater_name")
    public String delegaterName;
    @TableField(value = "company_name")
    public String companyName;
    @TableField(value = "pay_account")
    public String payAccount;
    @TableField(value = "section")
    public String section;
    @TableField(value="phone_number")
    public String phoneNumber;


}
