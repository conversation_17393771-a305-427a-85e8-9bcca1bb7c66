package com.ziyun.repairsystem.repair.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wuwenze.poi.annotation.Excel;
import com.wuwenze.poi.annotation.ExcelField;
import com.ziyun.repairsystem.common.converter.TimeConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Value;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_repair_info")
@Excel("工单信息表")
public class RepairInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "REPAIR_ID", type = IdType.AUTO)
    @ExcelField(value = "序号")
    private Long repairId;

    @TableField("NAME")
    @ExcelField(value = "报障人")
    private String name;

    @TableField("MOBILE")
    @ExcelField(value = "联系方式")
    private String mobile;

    @TableField("TITLE")
    @ExcelField(value = "报障分类", writeConverterExp = "0=取号机,1=评价器,2=LED屏,3=综合屏,4=高速球,5=监控半球,6=拾音器,7=硬盘录像机,99=其他")
    private String title;

    @TableField("ADDRESS")
    @ExcelField(value = "报障地址")
    private String address;

    @TableField("STATE_TAG")
    @ExcelField(value = "处理状态", writeConverterExp = "0=待审核,1=待处理,2=处理中,3=部分完成,4=处理完成")
    private String stateTag;

    @TableField("REPAIR_NUMBER")
    @ExcelField(value = "工单号")
    private String repairNumber;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField("ADD_TIME")
    @ExcelField(value = "报障时间", writeConverter = TimeConverter.class)
    private Date addTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField("REVIEW_TIME")
    @ExcelField(value = "审核时间", writeConverter = TimeConverter.class)
    private Date reviewTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField("DEAL_TIME")
    @ExcelField(value = "处理时间", writeConverter = TimeConverter.class)
    private Date dealTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField("PARTIAL_COMPLETION_TIME")
    @ExcelField(value = "部分完成时间", writeConverter = TimeConverter.class)
    private Date partialCompletionTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @TableField("COMPLETION_TIME")
    @ExcelField(value = "完成时间", writeConverter = TimeConverter.class)
    private Date completionTime;

    @TableField("CONT")
    @ExcelField(value = "报障内容")
    private String cont;

    @TableField("IMG")
    @ExcelField(value = "图片列表")
    private String img;

    @TableField("REVIEWS")
    @ExcelField(value = "反馈信息")
    private String reviews;

    @TableField("HANDLE_CONT")
    @ExcelField(value = "处理内容")
    private String handleCont;

    @TableField("RECEIPT")
    @ExcelField(value = "是否有回单")
    private Integer receipt;

    @TableField("SWJGDM")
    @ExcelField(value = "税务机登记序号")
    private String swjgdm;

    @TableField("BSDTMC")
    @ExcelField(value = "办税大厅名称")
    private String bsdtmc;

    @TableField("USER_ID")
    @ExcelField(value = "维护人员ID")
    private Long userId;

    @TableField("REAL_NAME")
    @ExcelField(value = "维护人")
    private String realName;

    @TableField("PROCESSOR_ID")
    @ExcelField(value = "处理人ID")
    private Long processorId;

    @TableField("DEAL_TYPE")
    @ExcelField(value = "处理方式", writeConverterExp = "0=上门处理,1=电话处理,2=省局平台处理,3=代维处理")
    private String dealType;

    // 用于搜索条件中的时间字段
    private transient String addTimeFrom;
    private transient String addTimeTo;


}
