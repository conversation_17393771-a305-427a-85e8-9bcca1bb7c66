package com.ziyun.repairsystem.repair.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class StaffInfo implements Serializable {

    private static final long serialVersionUID = 1L;

            @TableId(value = "STAFF_ID", type = IdType.AUTO)
    private Long staffId;

        @TableField("USER_ID")
    private Long userId;

        @TableField("USERNAME")
    private String username;

        @TableField("JOB_NUMBER")
    private Long jobNumber;

        @TableField("ADD_TIME")
    private LocalDateTime addTime;

        @TableField("ADD_USER")
    private String addUser;


}
