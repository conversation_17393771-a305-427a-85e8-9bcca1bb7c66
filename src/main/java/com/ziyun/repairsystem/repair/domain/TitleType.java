package com.ziyun.repairsystem.repair.domain;


public enum TitleType {
    QHJ("取号机", "0"), PJQ("评价器", "1"), LED("LED屏", "2"), ZHP("综合屏", "3"), GSQ("高速球", "4"), JKBQ("监控半球", "5"), SYQ("拾音器", "6"), YPLXJ("硬盘录像机", "7"), QT("其他", "99");

    private String name;
    private String index;
    //构造方法必须是private或者默认

    // 构造方法
    private TitleType(String name, String index) {
        this.name = name;
        this.index = index;
    }

    // 普通方法  
    public static String getName(String index){
        for(TitleType t: TitleType.values()){
            if(t.getIndex()==index){
                return t.name;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    //    public TitleType  valueOf(String value) {    //手写的从int到enum的转换函数
//        switch (value) {
//            case "0":
//                return QHJ;
//            case "1":
//                return PJQ;
//            case "2":
//                return LED;
//            case "3":
//                return ZHP;
//            case "4":
//                return GSQ;
//            case "5":
//                return JKBQ;
//            case "6":
//                return SYQ;
//            case "7":
//                return YPLXJ;
//            case "99":
//                return QT;
//             default:
//                 return null;
//        }
//    }
}
