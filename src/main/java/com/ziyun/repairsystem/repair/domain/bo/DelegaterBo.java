package com.ziyun.repairsystem.repair.domain.bo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ziyun.repairsystem.common.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class DelegaterBo extends BaseEntity {
    @NotNull(message = "不能为空")
    private Integer id;

    public String delegaterName;

    public String companyName;

    public String payAccount;

    public String responsibleArea;
}
