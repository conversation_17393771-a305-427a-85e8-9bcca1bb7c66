package com.ziyun.repairsystem.repair.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@TableName("t_delegater_district")
@NoArgsConstructor
@AllArgsConstructor
public class DelegaterDistrict {

    /**
     * 代维人员ID
     */
    @TableId(type = IdType.INPUT)
    private Integer delegaterId;

    /**
     * 区域ID
     */
    private String bsdtdjxh;

}
