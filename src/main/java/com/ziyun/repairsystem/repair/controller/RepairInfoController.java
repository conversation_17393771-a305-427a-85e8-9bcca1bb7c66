package com.ziyun.repairsystem.repair.controller;


import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.wuwenze.poi.ExcelKit;
import com.ziyun.repairsystem.common.domain.ResponseBo;
import com.ziyun.repairsystem.common.domain.SmsProperties;
import com.ziyun.repairsystem.common.service.SmsService;
import com.ziyun.repairsystem.common.utils.DateUtil;
import com.ziyun.repairsystem.common.utils.PhoneFormatCheckUtils;
import com.ziyun.repairsystem.repair.domain.District;
import com.ziyun.repairsystem.repair.domain.RepairInfo;
import com.ziyun.repairsystem.repair.service.DistrictService;
import com.ziyun.repairsystem.repair.service.RepairInfoService;
import com.ziyun.repairsystem.common.annotation.Log;
import com.ziyun.repairsystem.common.controller.BaseController;
import com.ziyun.repairsystem.common.domain.QueryRequest;
import com.ziyun.repairsystem.common.exception.ZiYunException;
import com.ziyun.repairsystem.system.domain.User;
import com.ziyun.repairsystem.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@CrossOrigin
@RequestMapping("repair")
public class RepairInfoController extends BaseController {
    private String message;
    @Autowired
    private RepairInfoService repairInfoService;
    @Autowired
    private DistrictService districtService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private UserService userService;
    @Autowired
    private SmsProperties smsProperties;

    @GetMapping
    public Map<String, Object> RepairInfoList(QueryRequest request, HttpServletRequest rq) {
        Map<String, Object> reMap = new HashMap<>();
        RepairInfo repairInfo = new RepairInfo();
        if(StringUtils.isNotBlank(rq.getParameter("repairId"))){
            repairInfo.setRepairId(Long.valueOf(rq.getParameter("repairId")));
        }
        if (StringUtils.isNotBlank(rq.getParameter("title"))) {
            repairInfo.setTitle(rq.getParameter("title"));
        }
        if (StringUtils.isNotBlank(rq.getParameter("swjgdm"))) {
            District district = districtService.findById(rq.getParameter("swjgdm"));
            if (district != null) {
                if (district.getLevel().equals(1)) {
                    repairInfo.setSwjgdm(rq.getParameter("swjgdm").substring(0, 3));
                } else if (district.getLevel().equals(2)) {
                    repairInfo.setSwjgdm(rq.getParameter("swjgdm").substring(0, 5));
                } else if (district.getLevel().equals(3)) {
                    repairInfo.setSwjgdm(rq.getParameter("swjgdm").substring(0, 7));
                }
            } else {
                repairInfo.setSwjgdm(rq.getParameter("swjgdm"));
            }
        }
        if (StringUtils.isNotBlank(rq.getParameter("stateTag"))) {
            repairInfo.setStateTag(rq.getParameter("stateTag"));
        }
        if(StringUtils.isNotBlank(rq.getParameter("userId"))){
            repairInfo.setUserId(Long.valueOf(rq.getParameter("userId")));
        }
        if (StringUtils.isNotBlank(rq.getParameter("addTimeFrom"))) {
//            Date cpTime = DateUtil.stringToDate(completionTime);
            repairInfo.setAddTimeFrom(rq.getParameter("addTimeFrom"));
        }
        if (StringUtils.isNotBlank(rq.getParameter("addTimeTo"))) {
//            Date cpTime = DateUtil.stringToDate(completionTime);
            repairInfo.setAddTimeTo(rq.getParameter("addTimeTo"));
        }
        reMap = getDataTable(this.repairInfoService.findRepairInfoes(request, repairInfo));
        return reMap;
    }

    @Log("新增工单")
    @PostMapping
//    @RequiresPermissions("repair:add")
    public void addRepairInfo(@Valid RepairInfo repairInfo) throws ZiYunException {
        try {
            this.repairInfoService.saveRepairInfo(repairInfo);
        } catch (Exception e) {
            message = "新增工单成功";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }

    @Log("删除工单")
    @DeleteMapping("/{repairIds}")
//    @RequiresPermissions("repair:delete")
    public void deleteRepairInfoes(@NotBlank(message = "{required}") @PathVariable String repairIds) throws ZiYunException {
        try {
            String[] ids = repairIds.split(StringPool.COMMA);
            this.repairInfoService.deleteRepairInfoes(ids);
        } catch (Exception e) {
            message = "删除工单成功";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }

    @Log("修改工单")
    @PutMapping
    public ResponseBo updateRepair(
            @RequestParam(value = "repairId", required = false) String repairId,
            @RequestParam(value = "stateTag", required = false) String stateTag,
            @RequestParam(value = "completionTime", required = false) String completionTime,
            @RequestParam(value = "handleCont", required = false) String handleCont,
            @RequestParam(value = "userId", required = false) String userId,
            @RequestParam(value = "receipt", required = false) Integer receipt,
            @RequestParam(value = "processorId",required = false) String processorId,
            @RequestParam(value = "dealType",required = false) String dealType
    ) throws ZiYunException {
        try {
            if (StringUtils.isNotBlank(repairId)) {
                RepairInfo repairInfo = repairInfoService.findById(Long.valueOf(repairId));
                //根据传来的stateTag和数据库中的字段获取比对进行更新操作
                if (stateTag.equals("1")) {
                    repairInfo.setStateTag(stateTag);
                    if (null == repairInfo.getReviewTime()) {
                        repairInfo.setReviewTime(new Date());
                    }
                    //发送审核短信sendSms
                    if (null != repairInfo.getMobile() && PhoneFormatCheckUtils.isPhoneLegal(repairInfo.getMobile())) {
                        smsService.sendSms(repairInfo.getMobile(), smsProperties.getSmscode_8(), repairInfo.getRepairNumber());
                        log.info("发送审核短信");
                    }
                } else if (stateTag.equals("2")) {
                    repairInfo.setStateTag(stateTag);
                    if (null == repairInfo.getDealTime()) {
                        repairInfo.setDealTime(new Date());
                    }
                    repairInfo.setProcessorId(Long.valueOf(processorId));
                } else if (stateTag.equals("3")) {
                    repairInfo.setStateTag(stateTag);
                    if (null == repairInfo.getPartialCompletionTime()) {
                        repairInfo.setPartialCompletionTime(new Date());
                    }
//                    repairInfo.setDealType(dealType);
                } else if (stateTag.equals("4")) {
                    if(!repairInfo.getStateTag().equals(stateTag)){
                        repairInfo.setStateTag(stateTag);
                        //sendSms
                        if (null != repairInfo.getMobile() && PhoneFormatCheckUtils.isPhoneLegal(repairInfo.getMobile())) {
                            smsService.sendSms(repairInfo.getMobile(), smsProperties.getSmscode_2(), repairInfo.getRepairNumber());
                            log.info("发送短信");
                        }
                    }
                    repairInfo.setCompletionTime(DateUtil.stringToDate(completionTime));
                    repairInfo.setProcessorId(Long.valueOf(processorId));
                    repairInfo.setHandleCont(handleCont);
                    repairInfo.setReceipt(receipt);
//                    repairInfo.setDealType(dealType);
                    //如果传过来的completionTime值不为空
//                    if (!completionTime.isEmpty()) {
//                        if (StringUtils.isNotBlank(completionTime) && null == repairInfo.getCompletionTime()) {
//                            Date cpTime = DateUtil.stringToDate(completionTime);
//                            repairInfo.setCompletionTime(cpTime);
//                        } else {
//                            Date cpTime = DateUtil.stringToDate(completionTime);
//                            repairInfo.setCompletionTime(cpTime);
//                        }
//                    }
                }
                //通过获取的userId更新userId,realName字段
                if (StringUtils.isNotBlank(userId)) {
                    User user = userService.findById(Long.valueOf(userId));
                    repairInfo.setUserId(user.getUserId());
                    repairInfo.setRealName(user.getRealName());
                }
                if(null!=dealType){
                    repairInfo.setDealType(dealType);
                }
                //更新反馈信息
                if (StringUtils.isNotBlank(handleCont)) {
                    repairInfo.setHandleCont(handleCont);
                }
                if (null!=receipt) {
                    repairInfo.setReceipt(receipt);
                }else {
                    repairInfo.setReceipt(0);
                }
                repairInfoService.updateRepairInfo(repairInfo);
                return ResponseBo.ok("修改成功!");
            }
            return ResponseBo.ok();
        } catch (Exception e) {
            message = "修改工单失败";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }

    @GetMapping("/statistics")
    public ResponseBo repairStatistics(@RequestParam Map<String, Object> param) throws ZiYunException {
        try {
            List<String> list = new ArrayList<>();
            for (int i = 0; i < 9; i++) {
                list.add(Integer.valueOf(i).toString());
            }
            list.add("99");
            RepairInfo repairInfo = new RepairInfo();
            Map<String, Object> titleMap = new HashMap<>();
            List<Map<String, Object>> reList = new ArrayList<>();
            for (String title : list) {
                switch (title) {
                    case "0":
                        titleMap = new HashMap<>();
                        repairInfo.setTitle(title);
                        titleMap.put("name", "取号机");
                        titleMap.put("value", repairInfoService.repairStatistics(repairInfo));
                        reList.add(titleMap);
                        break;
                    case "1":
                        titleMap = new HashMap<>();
                        repairInfo.setTitle(title);
                        titleMap.put("name", "评价器");
                        titleMap.put("value", repairInfoService.repairStatistics(repairInfo));
                        reList.add(titleMap);
                        break;
                    case "2":
                        titleMap = new HashMap<>();
                        repairInfo.setTitle(title);
                        titleMap.put("name", "LED屏");
                        titleMap.put("value", repairInfoService.repairStatistics(repairInfo));
                        reList.add(titleMap);
                        break;
                    case "3":
                        titleMap = new HashMap<>();
                        repairInfo.setTitle(title);
                        titleMap.put("name", "综合屏");
                        titleMap.put("value", repairInfoService.repairStatistics(repairInfo));
                        reList.add(titleMap);
                        break;
                    case "4":
                        titleMap = new HashMap<>();
                        repairInfo.setTitle(title);
                        titleMap.put("name", "高速球");
                        titleMap.put("value", repairInfoService.repairStatistics(repairInfo));
                        reList.add(titleMap);
                        break;
                    case "5":
                        titleMap = new HashMap<>();
                        repairInfo.setTitle(title);
                        titleMap.put("name", "监控半球");
                        titleMap.put("value", repairInfoService.repairStatistics(repairInfo));
                        reList.add(titleMap);
                        break;
                    case "6":
                        titleMap = new HashMap<>();
                        repairInfo.setTitle(title);
                        titleMap.put("name", "拾音器");
                        titleMap.put("value", repairInfoService.repairStatistics(repairInfo));
                        reList.add(titleMap);
                        break;
                    case "7":
                        titleMap = new HashMap<>();
                        repairInfo.setTitle(title);
                        titleMap.put("name", "硬盘录像机");
                        titleMap.put("value", repairInfoService.repairStatistics(repairInfo));
                        reList.add(titleMap);
                        break;
                    case "99":
                        titleMap = new HashMap<>();
                        repairInfo.setTitle(title);
                        titleMap.put("name", "其他");
                        titleMap.put("value", repairInfoService.repairStatistics(repairInfo));
                        reList.add(titleMap);
                        break;
                }
            }
            log.info(reList.toString());
            return ResponseBo.ok(reList);
        } catch (Exception e) {
            message = "查询失败";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }


    @PostMapping("/statisticsByCity")
    public ResponseBo statisticsByCity(@RequestParam(value = "CityCode", required = false) String CityCode) throws ZiYunException {
        try {
            List<Map<String, Object>> reList = repairInfoService.repairTitleStatisticsByCity(CityCode);
            return ResponseBo.ok(reList);
        } catch (Exception e) {
            message = "查询失败";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }

    @PostMapping("/analysis")
    public ResponseBo analysisByCity(@RequestParam(value = "CityCode", required = false) String CityCode) throws ZiYunException {
        try {
            Map<String, Object> reList = repairInfoService.analysisByCityCode(CityCode);
            return ResponseBo.ok(reList);
        } catch (Exception e) {
            message = "查询失败";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }

    @PostMapping("/analysisByMonth")
    public ResponseBo analysisByMonth(Integer month) throws ZiYunException {
        try {
            Map<String, Object> reList = repairInfoService.analysisByMonth(month);
            return ResponseBo.ok(reList);
        } catch (Exception e) {
            message = "查询失败";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }
}
