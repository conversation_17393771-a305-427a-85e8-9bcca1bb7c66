package com.ziyun.repairsystem.repair.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.ziyun.repairsystem.common.controller.BaseController;
import com.ziyun.repairsystem.common.domain.QueryRequest;
import com.ziyun.repairsystem.common.domain.ResponseBo;
import com.ziyun.repairsystem.common.exception.ZiYunException;
import com.ziyun.repairsystem.repair.domain.District;
import com.ziyun.repairsystem.repair.domain.RepairInfo;
import com.ziyun.repairsystem.repair.domain.bo.DelegaterBo;
import com.ziyun.repairsystem.repair.domain.vo.DelegaterDistrictVo;
import com.ziyun.repairsystem.repair.domain.vo.DelegaterVo;
import com.ziyun.repairsystem.repair.service.DelegaterService;
import com.ziyun.repairsystem.system.domain.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.xmlbeans.impl.jam.JParameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("delegater")
public class DelegaterController extends BaseController {
    private String message;
    @Autowired
    private DelegaterService delegaterService;

    @GetMapping("/list")
    public Map<String, Object> DelegaterList(){
            Map<String, Object> reMap = new HashMap<>();
                List<DelegaterVo> list = delegaterService.findAllDelegateres();
                reMap.put("data",list);
            return ResponseBo.ok(reMap);

    }

    @GetMapping("/list/{section}")
    public  Map<String, Object> findDelegaterById(@PathVariable Integer section){
            Map<String, Object> reMap = new HashMap<>();
            List<DelegaterVo> list = delegaterService.findAllDelegateresBySection(section.toString());
            reMap.put("data",list);
        return ResponseBo.ok(reMap);
    }

    @PostMapping()
    public void addDelegater(@RequestParam Map<String, Object> param) throws ZiYunException {
        try {
            DelegaterVo vo = new DelegaterVo();
            vo.setName(param.get("name").toString());
            vo.setSection(param.get("section").toString());
            vo.setCompanyName(param.get("companyName").toString());
            vo.setPhoneNumber(param.get("phoneNumber").toString());
            vo.setPayAccount(param.get("payAccount").toString());
            List<DelegaterDistrictVo> list = JSON.parseArray(param.get("unitList").toString(),DelegaterDistrictVo.class);
            vo.setUnitList(list);
            this.delegaterService.insertDelegater(vo);
        } catch (Exception e) {
            message = "新增代维成功";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }

    @DeleteMapping("/{delegaterIds}")
    public void deleteDelegateres(@NotBlank(message = "{required}") @PathVariable String delegaterIds) throws ZiYunException {
        try {
            String[] ids = delegaterIds.split(StringPool.COMMA);
            this.delegaterService.deleteDelegateres(ids);
        } catch (Exception e) {
            message = "删除成功";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }

    @PutMapping()
    public ResponseBo updateDelegatere(@RequestParam Map<String, Object> param
    ) throws ZiYunException {
        try {
            if (param.get("id")!=null) {
                DelegaterVo vo = new DelegaterVo();
                vo.setId(Integer.valueOf(param.get("id").toString()));
                vo.setName(param.get("name").toString());
                vo.setSection(param.get("section").toString());
                vo.setCompanyName(param.get("companyName").toString());
                vo.setPhoneNumber(param.get("phoneNumber").toString());
                vo.setPayAccount(param.get("payAccount").toString());
                List<DelegaterDistrictVo> list = JSON.parseArray(param.get("unitList").toString(),DelegaterDistrictVo.class);
                vo.setUnitList(list);
                delegaterService.updateDelegater(vo);
                return ResponseBo.ok("修改成功!");
            }
            return ResponseBo.ok();
        } catch (Exception e) {
            message = "修改工单失败";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }
}
