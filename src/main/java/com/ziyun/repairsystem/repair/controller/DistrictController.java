package com.ziyun.repairsystem.repair.controller;


import com.ziyun.repairsystem.common.domain.QueryRequest;
import com.ziyun.repairsystem.repair.domain.District;
import com.ziyun.repairsystem.repair.service.DistrictService;
import com.ziyun.repairsystem.system.domain.Dept;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("district")
public class DistrictController {
    @Autowired
    private DistrictService districtService;

    @GetMapping
    public Map<String, Object> areaList(QueryRequest request, District district) {
        return this.districtService.findDistricts(request, district);
    }

//    @GetMapping("getCityJson")
    public Map<String, Object> getCityJson(QueryRequest request,District district){
        return this.districtService.findDistricts(request, district);
    }
}
