package com.ziyun.repairsystem.repair.controller;


import com.ziyun.repairsystem.common.controller.BaseController;
import com.ziyun.repairsystem.common.domain.ResponseBo;
import com.ziyun.repairsystem.common.exception.ZiYunException;
import com.ziyun.repairsystem.repair.service.MobileAreaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.xml.ws.Action;
import java.util.List;
import java.util.Map;

/**
 * 手机号码与区域绑定控制类
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("mobileArea")
public class MobileAreaController extends BaseController {

    @Autowired
    private MobileAreaService mobileAreaService;

    private String message;

    /**
     * 通过手机号码查询地区信息
     * @param mobile
     * @return
     * @throws ZiYunException
     */
    @PostMapping("/districtByMobile")
    public ResponseBo findDistrictByMobile(@RequestParam(value = "mobile", required = false) String mobile) throws ZiYunException {
        try {
            Map<String, Object> reMap = mobileAreaService.findDistrictByMobile(mobile);
            return ResponseBo.ok(reMap);
        } catch (Exception e) {
            message = "查询失败";
            log.error(message, e);
            throw new ZiYunException(message);
        }
    }
}
