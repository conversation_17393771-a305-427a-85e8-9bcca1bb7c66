package com.ziyun.repairsystem.common.domain;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "sms")
public class SmsProperties {
    private String product;
    private String domain;
    private String accessKeyId;
    private String accessKeySecret;
    private String signName;
    private String smscode_1;
    private String smscode_2;
    private String smscode_3;
    private String smscode_4;
    private String smscode_5;
    private String smscode_6;
    private String smscode_7;
    private String smscode_8;
    private String phoneNumbers;

}
