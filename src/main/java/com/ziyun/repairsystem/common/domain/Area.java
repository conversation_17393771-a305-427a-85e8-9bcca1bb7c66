package com.ziyun.repairsystem.common.domain;

import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class Area<T> {
    /**
     * 节点ID
     */
    private String value;
    /**
     * 显示节点文本
     */
    private String label;

    /**
     * 节点的子节点
     */
    private List<Area<T>> children = new ArrayList<Area<T>>();

    public Area(String value, String label,
                List<Area<T>> children) {
        super();
        this.value = value;
        this.label = label;
        this.children = children;
    }

    public Area() {
        super();
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

}
