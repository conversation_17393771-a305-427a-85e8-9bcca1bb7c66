package com.ziyun.repairsystem.common.domain;

/**
 * ZIYUN常量
 */
public class ZiYunConstant {

    // user缓存前缀
    public static final String USER_CACHE_PREFIX = "ziyun.cache.user.";
    // user角色缓存前缀
    public static final String USER_ROLE_CACHE_PREFIX = "ziyun.cache.user.role.";
    // user权限缓存前缀
    public static final String USER_PERMISSION_CACHE_PREFIX = "ziyun.cache.user.permission.";
    // user个性化配置前缀
    public static final String USER_CONFIG_CACHE_PREFIX = "ziyun.cache.user.config.";
    // token缓存前缀
    public static final String TOKEN_CACHE_PREFIX = "ziyun.cache.token.";

    // 存储在线用户的 zset前缀
    public static final String ACTIVE_USERS_ZSET_PREFIX = "ziyun.user.active";

    // 排序规则： descend 降序
    public static final String ORDER_DESC = "descend";
    // 排序规则： ascend 升序
    public static final String ORDER_ASC = "ascend";

    // 按钮
    public static final String TYPE_BUTTON = "1";
    // 菜单
    public static final String TYPE_MENU = "0";

}
