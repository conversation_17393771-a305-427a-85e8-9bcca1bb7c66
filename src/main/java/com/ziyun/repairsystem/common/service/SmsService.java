package com.ziyun.repairsystem.common.service;

import com.aliyuncs.dysmsapi.model.v20170525.QuerySendDetailsResponse;
import com.aliyuncs.dysmsapi.model.v20170525.SendBatchSmsResponse;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;

import java.util.Map;

public interface SmsService {

    SendSmsResponse sendSms(String phoneNumber, String templateCode, String templateParam) throws ClientException;

    SendSmsResponse sendSms(String templateCode,Map<String,String> templateParam)throws ClientException;

    SendBatchSmsResponse sendBatchSms(String templateCode, Map<String, String> templateParam) throws ClientException;


    QuerySendDetailsResponse querySendDetails(String bizId, String phoneNumber) throws ClientException;


}
