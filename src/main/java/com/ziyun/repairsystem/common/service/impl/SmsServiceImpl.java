package com.ziyun.repairsystem.common.service.impl;


import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.*;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.ziyun.repairsystem.common.domain.SmsProperties;
import com.ziyun.repairsystem.common.service.SmsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

@Service("smsService")
public class SmsServiceImpl implements SmsService {
//    static final String product = "Dysmsapi";
    //产品域名,开发者无需替换
//    static final String domain = "dysmsapi.aliyuncs.com";

    // TODO 此处需要替换成开发者自己的AK(在阿里云访问控制台寻找)
//    static final String accessKeyId = "LTAIAOE6Z8j3RQkd";
//    static final String accessKeySecret = "jG6bo25ocdrEB5yCx06zYh9d4qoha1";

    @Autowired
    private SmsProperties smsProperties;

    public SendSmsResponse sendSms(String phoneNumber, String templateCode, String templateParam) throws ClientException {

        //可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");

        //初始化acsClient,暂不支持region化
        IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", smsProperties.getAccessKeyId(), smsProperties.getAccessKeySecret());
        DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", smsProperties.getProduct(), smsProperties.getDomain());
        IAcsClient acsClient = new DefaultAcsClient(profile);

        //组装请求对象-具体描述见控制台-文档部分内容
        SendSmsRequest request = new SendSmsRequest();
        //必填:待发送手机号
        request.setPhoneNumbers(phoneNumber);
        //必填:短信签名-可在短信控制台中找到
        request.setSignName(smsProperties.getSignName());
        //必填:短信模板-可在短信控制台中找到
        request.setTemplateCode(templateCode);

        //request.setTemplateParam("{\"name\":"++, \"code\":\"123\"}");
        //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
        request.setTemplateParam("{\"codeNumber\":" + templateParam + "}");


        //选填-上行短信扩展码(无特殊需求用户请忽略此字段)
        //request.setSmsUpExtendCode("90997");

        //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
        //request.setOutId("yourOutId");

        //hint 此处可能会抛出异常，注意catch
        SendSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);

        return sendSmsResponse;
    }

    @Override
    public SendSmsResponse sendSms(String templateCode, Map<String, String> templateParam) throws ClientException {
        //可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");
        //初始化acsClient,暂不支持region化
        IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", smsProperties.getAccessKeyId(), smsProperties.getAccessKeySecret());
        DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", smsProperties.getProduct(), smsProperties.getDomain());
        IAcsClient acsClient = new DefaultAcsClient(profile);
        //组装请求对象-具体描述见控制台-文档部分内容
        SendSmsRequest request = new SendSmsRequest();
//        //必填:待发送手机号
//        request.setPhoneNumbers(phoneNumber);
        //必填:短信签名-可在短信控制台中找到
        request.setSignName(smsProperties.getSignName());
        //必填:短信模板-可在短信控制台中找到
        request.setTemplateCode(templateCode);
        if (templateCode.equals(smsProperties.getSmscode_4())) {
            //呼叫短信通知
            //必填:待发送手机号
            request.setPhoneNumbers(templateParam.get("phone_num"));
            request.setTemplateCode(smsProperties.getSmscode_4());
            String jsonStr = "{\"taxhall\":\"" + templateParam.get("taxhall") + "\", \"biz_name\":\"" + templateParam.get("biz_name") + "\", \"tkt_id\":\"" + templateParam.get("tkt_id") + "\", \"win_name\":\"" + templateParam.get("win_name") + "\"}";
            request.setTemplateParam(jsonStr);
        } else if (templateCode.equals(smsProperties.getSmscode_5())) {
            //提醒短信通知
            request.setPhoneNumbers(templateParam.get("phone_num"));
            request.setTemplateCode(smsProperties.getSmscode_5());
            String jsonStr = "{\"taxhall\":\"" + templateParam.get("taxhall") + "\", \"biz_name\":\"" + templateParam.get("biz_name") + "\", \"tkt_id\":\"" + templateParam.get("tkt_id") + "\", \"status\":\"" + templateParam.get("status") + "\"}";
            request.setTemplateParam(jsonStr);
        } else if (templateCode.equals(smsProperties.getSmscode_6())) {
            //办结短信通知
            request.setPhoneNumbers(templateParam.get("phone_num"));
            request.setTemplateCode(smsProperties.getSmscode_6());
            String jsonStr = "{\"taxhall\":\"" + templateParam.get("taxhall") + "\"}";
            request.setTemplateParam(jsonStr);
        }else if(templateCode.equals(smsProperties.getSmscode_7())){
            //取号短信
            request.setPhoneNumbers(templateParam.get("phone_num"));
            request.setTemplateCode(smsProperties.getSmscode_7());
            String jsonStr = "{\"taxhall\":\"" + templateParam.get("taxhall") + "\", \"biz_name\":\"" + templateParam.get("biz_name") + "\", \"tkt_id\":\"" + templateParam.get("tkt_id") + "\", \"status\":\"" + templateParam.get("status") + "\"}";
            request.setTemplateParam(jsonStr);
        }
        //hint 此处可能会抛出异常，注意catch

        SendSmsResponse sendSmsResponse = acsClient.getAcsResponse(request);
        return sendSmsResponse;
    }

    /**
     * @param templateCode
     * @param templateParam
     * @return
     * @throws ClientException
     */
    public SendBatchSmsResponse sendBatchSms(String templateCode, Map<String, String> templateParam) throws ClientException {
        //可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");
        //初始化acsClient,暂不支持region化
        IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", smsProperties.getAccessKeyId(), smsProperties.getAccessKeySecret());
        DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", smsProperties.getProduct(), smsProperties.getDomain());
        IAcsClient acsClient = new DefaultAcsClient(profile);
        //组装请求对象-具体描述见控制台-文档部分内容
        SendBatchSmsRequest request = new SendBatchSmsRequest();
        //必填:待发送手机号
        request.setPhoneNumberJson(smsProperties.getPhoneNumbers().toString());
        //必填:短信签名-可在短信控制台中找到
        request.setSignNameJson("[\"" + smsProperties.getSignName() + "\",\"" + smsProperties.getSignName() + "\"]");
        //必填:短信模板-可在短信控制台中找到
        request.setTemplateCode(templateCode);
        //request.setTemplateParam("{\"name\":"++, \"code\":\"123\"}");
        //模版拼接
        String jsonStr = "{\"codeNumber\":\"" + templateParam.get("codeNumber") + "\", \"name\":\"" + templateParam.get("name") + "\", \"phoneNumber\":\"" + templateParam.get("phoneNumber") + "\"}";
        //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
        request.setTemplateParamJson("[" + jsonStr + "," + jsonStr + "]");
        //选填-上行短信扩展码(无特殊需求用户请忽略此字段)
        //request.setSmsUpExtendCode("90997");
        //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
        //request.setOutId("yourOutId");
        //hint 此处可能会抛出异常，注意catch
        SendBatchSmsResponse sendBatchSmsResponse = acsClient.getAcsResponse(request);
        return sendBatchSmsResponse;
    }


    public QuerySendDetailsResponse querySendDetails(String bizId, String phoneNumber) throws ClientException {

        //可自助调整超时时间
        System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
        System.setProperty("sun.net.client.defaultReadTimeout", "10000");

        //初始化acsClient,暂不支持region化
        IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", smsProperties.getAccessKeyId(), smsProperties.getAccessKeySecret());
        DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", smsProperties.getProduct(), smsProperties.getDomain());
        IAcsClient acsClient = new DefaultAcsClient(profile);

        //组装请求对象
        QuerySendDetailsRequest request = new QuerySendDetailsRequest();
        //必填-号码
        request.setPhoneNumber(phoneNumber);
        //可选-流水号
        request.setBizId(bizId);
        //必填-发送日期 支持30天内记录查询，格式yyyyMMdd
        SimpleDateFormat ft = new SimpleDateFormat("yyyyMMdd");
        request.setSendDate(ft.format(new Date()));
        //必填-页大小
        request.setPageSize(10L);
        //必填-当前页码从1开始计数
        request.setCurrentPage(1L);

        //hint 此处可能会抛出异常，注意catch
        QuerySendDetailsResponse querySendDetailsResponse = acsClient.getAcsResponse(request);
        return querySendDetailsResponse;
    }
}
