//package com.ziyun.repairsystem.common.service.impl;
//
//import com.aliyun.dingtalkoauth2_1_0.Client;
//import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest;
//import com.aliyun.tea.TeaException;
//import com.dingtalk.api.DefaultDingTalkClient;
//import com.dingtalk.api.request.OapiGettokenRequest;
//import com.dingtalk.api.response.OapiGettokenResponse;
//import com.taobao.api.ApiException;
//import com.ziyun.repairsystem.common.domain.SmsProperties;
//import com.ziyun.repairsystem.common.properties.DingTalkProperties;
//import com.ziyun.repairsystem.common.service.DingTalkService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import static com.taobao.api.Constants.APP_KEY;
//
//@Service("dingtalkService")
//public class DingTalkServiceImpl implements DingTalkService {
//    @Autowired
//    private DingTalkProperties dingTalkProperties;
//
////    @Override
////    public String getAccessToken() {
////        // 创建钉钉客户端实例
//////        DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken", APP_KEY, APP_SECRET);
////
////        // 创建获取access_token的请求对象
////        OapiGettokenRequest request = new OapiGettokenRequest();
////        request.setAppkey(dingTalkProperties.getAppKey());
////        request.setAppsecret(dingTalkProperties.getAppSecret());
////
////        // 发送请求并获取响应
////        OapiGettokenResponse response = null;
////        try {
//////            response = client.execute(request);
//////        } catch (ApiException e) {
//////            e.printStackTrace();
//////        }
////
////        // 返回access_token
////        if (response != null && response.getErrcode() == 0) {
////            return response.getAccessToken();
////        } else {
////            return null;
////        }
////    }
////
////
////}
//}