package com.ziyun.repairsystem.common.utils;

import com.ziyun.repairsystem.common.domain.Area;

import java.util.ArrayList;
import java.util.List;

public class AreaUtils {

//    public static <T> Area<T> build(List<Area<T>> nodes) {
//        if (nodes == null) {
//            return null;
//        }
//        List<Area<T>> topNodes = new ArrayList<Area<T>>();
//        for (Area<T> children : nodes) {
//            String pid = children.getParentId();
//            if (pid == null || "0".equals(pid)) {
//                topNodes.add(children);
//                continue;
//            }
//            for (Area<T> parent : nodes) {
////                String id = parent.getId();
////                if (id != null && id.equals(pid)) {
////                    parent.getChildren().add(children);
////                    children.setHasParent(true);
////                    parent.setChildren(true);
//                    continue;
//                }
//            }
//        }

//        Area<T> root = new Area<T>();
//        root.setId("0");
//        root.setParentId("");
//        root.setHasParent(false);
//        root.setChildren(true);
//        root.setChildren(topNodes);
//		root.setText("根节点");
//        return null;
//    }

//    public static <T> Area<T> buildOfParam(List<Area<T>> nodes, String pid) {
//        if (nodes == null) {
//            return null;
//        }
//        List<Area<T>> topNodes = new ArrayList<Area<T>>();
//        for (Area<T> children : nodes) {
//            String cPid = children.getParentId();
//            if (pid == null || pid.equals(cPid)) {
//                topNodes.add(children);
//                continue;
//            }
//            for (Area<T> parent : nodes) {
//                String id = parent.getValue();
//                if (id != null && id.equals(cPid)) {
//                    parent.getChildren().add(children);
//                    continue;
//                }
//            }
//        }
//
//        Area<T> root = new Area<T>();
//        root.setValue(pid);
//        root.setParentId("");
//        root.setChildren(topNodes);
////		root.setText("根节点");
//        return root;
//    }

//    public static <T> List<Area<T>> buildList(List<Area<T>> nodes, String idParam) {
//        if (nodes == null) {
//            return null;
//        }
//        List<Area<T>> topNodes = new ArrayList<Area<T>>();
//        for (Area<T> children : nodes) {
//            String pid = children.getParentId();
//            if (pid == null || idParam.equals(pid)) {
//                topNodes.add(children);
//                continue;
//            }
//            for (Area<T> parent : nodes) {
//                String id = parent.getValue();
//                if (id != null && id.equals(pid)) {
//                    parent.getChildren().add(children);
//                    continue;
//                }
//            }
//        }
//        return topNodes;
//    }
}