package com.ziyun.repairsystem.common.controller;

import com.aliyun.dingtalkdoc_1_0.models.UpdateRangeHeaders;
import com.aliyun.dingtalkdoc_1_0.models.UpdateRangeRequest;
import com.aliyun.dingtalkoauth2_1_0.Client;
import com.aliyun.dingtalkoauth2_1_0.models.GetAccessTokenRequest;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.request.OapiV2UserGetbymobileRequest;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.dingtalk.api.response.OapiV2UserGetbymobileResponse;
import com.taobao.api.ApiException;
import com.ziyun.repairsystem.common.exception.ZiYunException;
import com.ziyun.repairsystem.common.properties.DingTalkProperties;
import com.ziyun.repairsystem.common.utils.DateUtil;
import com.ziyun.repairsystem.repair.domain.RepairInfo;
import com.ziyun.repairsystem.repair.service.DistrictService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Slf4j
@RestController
@RequestMapping("dingtalk")
public class DingTalkController extends BaseController {
    @Autowired
    private DingTalkProperties dingTalkProperties;

    @Autowired
    private DistrictService districtService;

    @PostMapping("/upload")
    @ResponseBody
    public void uploadDingTalk(@RequestParam Map<String, String> rq) throws ZiYunException {
            try {
                String accessToken = getAccessToken();
                String userId = getUserId(accessToken);
                String operatorId = getUnionid(userId, accessToken);

                String currentDate = DateUtil.getDateFormat(new Date(), DateUtil.FULL_TIME_SPLIT_PATTERN);
                String unitNameValue = rq.get("unitName");
                String addressValue = rq.get("address");
                String addTimeValue = rq.get("addTime");
                String deviceValue = rq.get("device");
                String contValue = rq.get("cont");
                log.info("传过来的:"+contValue);
                if (contValue != null && contValue.contains("\n")) {
                    contValue = contValue.replace("\n", ",");
                }
                String swjgdm = rq.get("section");

                String section =  districtService.concatRegion(swjgdm);

                List<String> dataList = new ArrayList<>();
                dataList.add(currentDate);
                dataList.add(section);
                dataList.add(unitNameValue);
                dataList.add(addressValue);
                dataList.add(addTimeValue);
                dataList.add(deviceValue);
                dataList.add(contValue);



                insertRow(accessToken, operatorId);
                log.info("Inserted data: " + dataList);
                updateExcel(accessToken, operatorId, dataList);
            } catch (Exception e) {
                log.error("Error during uploadDingTalk", e);
                throw new ZiYunException("Failed to upload data to DingTalk");
            }
    }

    private Config createClientConfig() {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return config;
    }

    private String getAccessToken() throws Exception {
        Client client = new Client(createClientConfig());
        GetAccessTokenRequest getAccessTokenRequest = new GetAccessTokenRequest()
                .setAppKey(dingTalkProperties.getAppKey())
                .setAppSecret(dingTalkProperties.getAppSecret());
        return client.getAccessToken(getAccessTokenRequest).getBody().getAccessToken();
    }

    private String getUserId(String accessToken) throws Exception {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/getbymobile");
        OapiV2UserGetbymobileRequest req = new OapiV2UserGetbymobileRequest();
        req.setMobile("15382312786");
        OapiV2UserGetbymobileResponse rsp = client.execute(req, accessToken);
        return rsp.getResult().getUserid();
    }

    private String getUnionid(String userId, String accessToken) throws Exception {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/get");
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(userId);
        OapiV2UserGetResponse rsp = client.execute(req, accessToken);
        return rsp.getResult().getUnionid();
    }

    private void insertRow(String accessToken, String operatorId) throws Exception {
        Config config = createClientConfig();
        com.aliyun.dingtalkdoc_1_0.Client client = new com.aliyun.dingtalkdoc_1_0.Client(config);
        com.aliyun.dingtalkdoc_1_0.models.InsertRowsBeforeHeaders insertRowsBeforeHeaders = new com.aliyun.dingtalkdoc_1_0.models.InsertRowsBeforeHeaders();
        insertRowsBeforeHeaders.xAcsDingtalkAccessToken = accessToken;
        com.aliyun.dingtalkdoc_1_0.models.InsertRowsBeforeRequest insertRowsBeforeRequest = new com.aliyun.dingtalkdoc_1_0.models.InsertRowsBeforeRequest()
                .setOperatorId(operatorId)
                .setRow(1L)  // You need to set the appropriate row value here.
                .setRowCount(1L);

        try {
            client.insertRowsBeforeWithOptions("KM7qe9zxDLRpqpj8", "Sheet1", insertRowsBeforeRequest, insertRowsBeforeHeaders, new com.aliyun.teautil.models.RuntimeOptions());
        } catch (TeaException err) {
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // Handle the exception as needed.
                log.error("Error during insertRow", err);
            }
        } catch (Exception _err) {
            TeaException err = new TeaException(_err.getMessage(), _err);
            if (!com.aliyun.teautil.Common.empty(err.code) && !com.aliyun.teautil.Common.empty(err.message)) {
                // Handle the exception as needed.
                log.error("Error during insertRow", err);
            }
        }
    }


    private String updateExcel(String accessToken, String operatorId, List<String> dataList) throws Exception {
        Config config = createClientConfig();
        com.aliyun.dingtalkdoc_1_0.Client client = new com.aliyun.dingtalkdoc_1_0.Client(config);

        UpdateRangeHeaders updateRangeHeaders = new UpdateRangeHeaders();
        updateRangeHeaders.xAcsDingtalkAccessToken = accessToken;

        UpdateRangeRequest updateRangeRequest = new UpdateRangeRequest()
                .setOperatorId(operatorId)
                .setValues(Arrays.asList(
                        dataList
                ));

        return client.updateRangeWithOptions("KM7qe9zxDLRpqpj8", "Sheet1", "A2:G2", updateRangeRequest, updateRangeHeaders, new RuntimeOptions()).toString();
    }
}
